<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional SVG Icon Library - Jermesa Studio</title>
    <meta name="description" content="Comprehensive collection of professional multicolor SVG icons for modern web design">
    <meta name="keywords" content="SVG icons, web design, UI icons, vector graphics, professional icons">
    
    <!-- Google Fonts - Open SIL License -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    
    <style>
        /* CSS Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --accent-color: #0ea5e9;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --background-color: #ffffff;
            --surface-color: #f8fafc;
            --border-color: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --border-radius: 8px;
            --transition: all 0.2s ease-in-out;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--background-color);
            font-size: 16px;
        }
        
        /* Header Styles */
        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            color: white;
            padding: 2rem 0;
            text-align: center;
            box-shadow: var(--shadow-lg);
        }
        
        .header h1 {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            margin-bottom: 0.5rem;
            letter-spacing: -0.025em;
        }
        
        .header p {
            font-size: clamp(1rem, 2vw, 1.25rem);
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        
        /* Container and Layout */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        
        .main-content {
            padding: 3rem 0;
        }
        
        /* Category Section Styles */
        .category-section {
            margin-bottom: 4rem;
        }
        
        .category-header {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--border-color);
        }
        
        .category-title {
            font-size: 1.75rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }
        
        .category-count {
            background: var(--primary-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            margin-left: 1rem;
        }
        
        /* Icon Grid Styles */
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }
        
        .icon-card {
            background: var(--surface-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            text-align: center;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        
        .icon-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
        }
        
        .icon-container {
            width: 64px;
            height: 64px;
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .icon-container svg {
            width: 100%;
            height: 100%;
            transition: var(--transition);
        }
        
        .icon-card:hover .icon-container svg {
            transform: scale(1.1);
        }
        
        .icon-name {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.75rem;
        }
        
        .copy-button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            font-family: 'JetBrains Mono', monospace;
        }
        
        .copy-button:hover {
            background: var(--accent-color);
            transform: translateY(-1px);
        }
        
        .copy-button:active {
            transform: translateY(0);
        }
        
        .copy-button.copied {
            background: var(--success-color);
        }
        
        /* Cookie Consent Banner */
        .cookie-banner {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--text-primary);
            color: white;
            padding: 1rem;
            z-index: 1000;
            transform: translateY(100%);
            transition: transform 0.3s ease-in-out;
        }
        
        .cookie-banner.show {
            transform: translateY(0);
        }
        
        .cookie-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .cookie-text {
            flex: 1;
            min-width: 300px;
        }
        
        .cookie-buttons {
            display: flex;
            gap: 0.75rem;
        }
        
        .cookie-button {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .cookie-accept {
            background: var(--success-color);
            color: white;
        }
        
        .cookie-decline {
            background: transparent;
            color: white;
            border: 1px solid white;
        }
        
        .cookie-button:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }
        
        /* Footer Styles */
        .footer {
            background: var(--surface-color);
            border-top: 1px solid var(--border-color);
            padding: 2rem 0;
            margin-top: 4rem;
        }
        
        .footer-content {
            text-align: center;
            color: var(--text-secondary);
        }
        
        .footer-links {
            margin-bottom: 1rem;
        }
        
        .footer-links a {
            color: var(--primary-color);
            text-decoration: none;
            margin: 0 1rem;
            font-weight: 500;
        }
        
        .footer-links a:hover {
            text-decoration: underline;
        }
        
        .attribution {
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }
        
        .font-attribution {
            font-size: 0.75rem;
            opacity: 0.8;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .icon-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                gap: 1rem;
            }
            
            .icon-card {
                padding: 1rem;
            }
            
            .icon-container {
                width: 48px;
                height: 48px;
            }
            
            .cookie-content {
                flex-direction: column;
                text-align: center;
            }
            
            .cookie-buttons {
                justify-content: center;
            }
        }
        
        @media (max-width: 480px) {
            .icon-grid {
                grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            }
            
            .category-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
            
            .category-count {
                margin-left: 0;
            }
        }
        
        /* Utility Classes */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <h1>Professional SVG Icon Library</h1>
            <p>Comprehensive collection of hand-coded multicolor SVG icons for modern web design</p>
            <div style="margin-top: 1rem; padding: 0.75rem 1.5rem; background: rgba(255,255,255,0.1); border-radius: 20px; display: inline-block;">
                <span style="font-size: 0.875rem; opacity: 0.9;">✨ Demonstration: 4 Categories • 34 Professional Icons • Full Library: 18 Categories</span>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Business Icons Category -->
            <section class="category-section">
                <div class="category-header">
                    <h2 class="category-title">Business Icons</h2>
                    <span class="category-count">10 icons</span>
                </div>
                <div class="icon-grid">
                    <!-- Briefcase Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="briefcaseGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#8B5CF6"/>
                                        <stop offset="100%" style="stop-color:#6366F1"/>
                                    </linearGradient>
                                </defs>
                                <rect x="8" y="20" width="48" height="32" rx="4" fill="url(#briefcaseGrad)"/>
                                <rect x="12" y="24" width="40" height="24" rx="2" fill="#F8FAFC" opacity="0.9"/>
                                <rect x="24" y="12" width="16" height="12" rx="2" fill="#64748B"/>
                                <rect x="26" y="14" width="12" height="8" rx="1" fill="#F1F5F9"/>
                                <circle cx="32" cy="36" r="3" fill="#6366F1"/>
                                <rect x="29" y="34" width="6" height="4" rx="1" fill="#FFFFFF"/>
                            </svg>
                        </div>
                        <div class="icon-name">Briefcase</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;briefcaseGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#8B5CF6&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#6366F1&quot;/></linearGradient></defs><rect x=&quot;8&quot; y=&quot;20&quot; width=&quot;48&quot; height=&quot;32&quot; rx=&quot;4&quot; fill=&quot;url(#briefcaseGrad)&quot;/><rect x=&quot;12&quot; y=&quot;24&quot; width=&quot;40&quot; height=&quot;24&quot; rx=&quot;2&quot; fill=&quot;#F8FAFC&quot; opacity=&quot;0.9&quot;/><rect x=&quot;24&quot; y=&quot;12&quot; width=&quot;16&quot; height=&quot;12&quot; rx=&quot;2&quot; fill=&quot;#64748B&quot;/><rect x=&quot;26&quot; y=&quot;14&quot; width=&quot;12&quot; height=&quot;8&quot; rx=&quot;1&quot; fill=&quot;#F1F5F9&quot;/><circle cx=&quot;32&quot; cy=&quot;36&quot; r=&quot;3&quot; fill=&quot;#6366F1&quot;/><rect x=&quot;29&quot; y=&quot;34&quot; width=&quot;6&quot; height=&quot;4&quot; rx=&quot;1&quot; fill=&quot;#FFFFFF&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Bar Chart Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="chartGrad1" x1="0%" y1="100%" x2="0%" y2="0%">
                                        <stop offset="0%" style="stop-color:#10B981"/>
                                        <stop offset="100%" style="stop-color:#34D399"/>
                                    </linearGradient>
                                    <linearGradient id="chartGrad2" x1="0%" y1="100%" x2="0%" y2="0%">
                                        <stop offset="0%" style="stop-color:#3B82F6"/>
                                        <stop offset="100%" style="stop-color:#60A5FA"/>
                                    </linearGradient>
                                    <linearGradient id="chartGrad3" x1="0%" y1="100%" x2="0%" y2="0%">
                                        <stop offset="0%" style="stop-color:#F59E0B"/>
                                        <stop offset="100%" style="stop-color:#FBBF24"/>
                                    </linearGradient>
                                </defs>
                                <rect x="8" y="8" width="48" height="48" rx="4" fill="#F8FAFC" stroke="#E2E8F0" stroke-width="2"/>
                                <rect x="14" y="36" width="8" height="16" rx="2" fill="url(#chartGrad1)"/>
                                <rect x="28" y="24" width="8" height="28" rx="2" fill="url(#chartGrad2)"/>
                                <rect x="42" y="30" width="8" height="22" rx="2" fill="url(#chartGrad3)"/>
                                <line x1="12" y1="52" x2="52" y2="52" stroke="#94A3B8" stroke-width="1"/>
                                <line x1="12" y1="52" x2="12" y2="12" stroke="#94A3B8" stroke-width="1"/>
                            </svg>
                        </div>
                        <div class="icon-name">Bar Chart</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;chartGrad1&quot; x1=&quot;0%&quot; y1=&quot;100%&quot; x2=&quot;0%&quot; y2=&quot;0%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#10B981&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#34D399&quot;/></linearGradient><linearGradient id=&quot;chartGrad2&quot; x1=&quot;0%&quot; y1=&quot;100%&quot; x2=&quot;0%&quot; y2=&quot;0%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#3B82F6&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#60A5FA&quot;/></linearGradient><linearGradient id=&quot;chartGrad3&quot; x1=&quot;0%&quot; y1=&quot;100%&quot; x2=&quot;0%&quot; y2=&quot;0%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#F59E0B&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#FBBF24&quot;/></linearGradient></defs><rect x=&quot;8&quot; y=&quot;8&quot; width=&quot;48&quot; height=&quot;48&quot; rx=&quot;4&quot; fill=&quot;#F8FAFC&quot; stroke=&quot;#E2E8F0&quot; stroke-width=&quot;2&quot;/><rect x=&quot;14&quot; y=&quot;36&quot; width=&quot;8&quot; height=&quot;16&quot; rx=&quot;2&quot; fill=&quot;url(#chartGrad1)&quot;/><rect x=&quot;28&quot; y=&quot;24&quot; width=&quot;8&quot; height=&quot;28&quot; rx=&quot;2&quot; fill=&quot;url(#chartGrad2)&quot;/><rect x=&quot;42&quot; y=&quot;30&quot; width=&quot;8&quot; height=&quot;22&quot; rx=&quot;2&quot; fill=&quot;url(#chartGrad3)&quot;/><line x1=&quot;12&quot; y1=&quot;52&quot; x2=&quot;52&quot; y2=&quot;52&quot; stroke=&quot;#94A3B8&quot; stroke-width=&quot;1&quot;/><line x1=&quot;12&quot; y1=&quot;52&quot; x2=&quot;12&quot; y2=&quot;12&quot; stroke=&quot;#94A3B8&quot; stroke-width=&quot;1&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Handshake Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="handGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#FBBF24"/>
                                        <stop offset="100%" style="stop-color:#F59E0B"/>
                                    </linearGradient>
                                    <linearGradient id="handGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#60A5FA"/>
                                        <stop offset="100%" style="stop-color:#3B82F6"/>
                                    </linearGradient>
                                </defs>
                                <path d="M12 32 Q16 28 24 32 L32 36 Q36 32 44 36 L52 32 Q56 36 52 40 L44 44 Q40 48 32 44 L24 40 Q20 36 12 40 Q8 36 12 32 Z" fill="url(#handGrad1)"/>
                                <path d="M20 36 Q24 32 32 36 L40 40 Q44 36 48 40 Q52 44 48 48 L40 52 Q36 56 28 52 L20 48 Q16 44 20 40 Q16 36 20 36 Z" fill="url(#handGrad2)"/>
                                <circle cx="28" cy="38" r="2" fill="#FFFFFF" opacity="0.8"/>
                                <circle cx="36" cy="42" r="2" fill="#FFFFFF" opacity="0.8"/>
                            </svg>
                        </div>
                        <div class="icon-name">Handshake</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;handGrad1&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#FBBF24&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#F59E0B&quot;/></linearGradient><linearGradient id=&quot;handGrad2&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#60A5FA&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#3B82F6&quot;/></linearGradient></defs><path d=&quot;M12 32 Q16 28 24 32 L32 36 Q36 32 44 36 L52 32 Q56 36 52 40 L44 44 Q40 48 32 44 L24 40 Q20 36 12 40 Q8 36 12 32 Z&quot; fill=&quot;url(#handGrad1)&quot;/><path d=&quot;M20 36 Q24 32 32 36 L40 40 Q44 36 48 40 Q52 44 48 48 L40 52 Q36 56 28 52 L20 48 Q16 44 20 40 Q16 36 20 36 Z&quot; fill=&quot;url(#handGrad2)&quot;/><circle cx=&quot;28&quot; cy=&quot;38&quot; r=&quot;2&quot; fill=&quot;#FFFFFF&quot; opacity=&quot;0.8&quot;/><circle cx=&quot;36&quot; cy=&quot;42&quot; r=&quot;2&quot; fill=&quot;#FFFFFF&quot; opacity=&quot;0.8&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Presentation Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="boardGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#F8FAFC"/>
                                        <stop offset="100%" style="stop-color:#E2E8F0"/>
                                    </linearGradient>
                                </defs>
                                <rect x="8" y="8" width="48" height="32" rx="4" fill="url(#boardGrad)" stroke="#94A3B8" stroke-width="2"/>
                                <path d="M16 16 L24 28 L32 20 L48 32" stroke="#10B981" stroke-width="3" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                                <circle cx="16" cy="16" r="2" fill="#10B981"/>
                                <circle cx="24" cy="28" r="2" fill="#10B981"/>
                                <circle cx="32" cy="20" r="2" fill="#10B981"/>
                                <circle cx="48" cy="32" r="2" fill="#10B981"/>
                                <line x1="32" y1="40" x2="32" y2="56" stroke="#64748B" stroke-width="3"/>
                                <line x1="20" y1="56" x2="44" y2="56" stroke="#64748B" stroke-width="3" stroke-linecap="round"/>
                            </svg>
                        </div>
                        <div class="icon-name">Presentation</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;boardGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#F8FAFC&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#E2E8F0&quot;/></linearGradient></defs><rect x=&quot;8&quot; y=&quot;8&quot; width=&quot;48&quot; height=&quot;32&quot; rx=&quot;4&quot; fill=&quot;url(#boardGrad)&quot; stroke=&quot;#94A3B8&quot; stroke-width=&quot;2&quot;/><path d=&quot;M16 16 L24 28 L32 20 L48 32&quot; stroke=&quot;#10B981&quot; stroke-width=&quot;3&quot; fill=&quot;none&quot; stroke-linecap=&quot;round&quot; stroke-linejoin=&quot;round&quot;/><circle cx=&quot;16&quot; cy=&quot;16&quot; r=&quot;2&quot; fill=&quot;#10B981&quot;/><circle cx=&quot;24&quot; cy=&quot;28&quot; r=&quot;2&quot; fill=&quot;#10B981&quot;/><circle cx=&quot;32&quot; cy=&quot;20&quot; r=&quot;2&quot; fill=&quot;#10B981&quot;/><circle cx=&quot;48&quot; cy=&quot;32&quot; r=&quot;2&quot; fill=&quot;#10B981&quot;/><line x1=&quot;32&quot; y1=&quot;40&quot; x2=&quot;32&quot; y2=&quot;56&quot; stroke=&quot;#64748B&quot; stroke-width=&quot;3&quot;/><line x1=&quot;20&quot; y1=&quot;56&quot; x2=&quot;44&quot; y2=&quot;56&quot; stroke=&quot;#64748B&quot; stroke-width=&quot;3&quot; stroke-linecap=&quot;round&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Calculator Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="calcGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#1F2937"/>
                                        <stop offset="100%" style="stop-color:#374151"/>
                                    </linearGradient>
                                </defs>
                                <rect x="12" y="8" width="40" height="48" rx="6" fill="url(#calcGrad)"/>
                                <rect x="16" y="12" width="32" height="12" rx="2" fill="#10B981"/>
                                <text x="32" y="21" text-anchor="middle" fill="white" font-family="monospace" font-size="8">123.45</text>
                                <rect x="16" y="28" width="6" height="6" rx="1" fill="#6B7280"/>
                                <rect x="25" y="28" width="6" height="6" rx="1" fill="#6B7280"/>
                                <rect x="34" y="28" width="6" height="6" rx="1" fill="#6B7280"/>
                                <rect x="43" y="28" width="6" height="15" rx="1" fill="#EF4444"/>
                                <rect x="16" y="37" width="6" height="6" rx="1" fill="#6B7280"/>
                                <rect x="25" y="37" width="6" height="6" rx="1" fill="#6B7280"/>
                                <rect x="34" y="37" width="6" height="6" rx="1" fill="#6B7280"/>
                                <rect x="16" y="46" width="15" height="6" rx="1" fill="#3B82F6"/>
                                <rect x="34" y="46" width="6" height="6" rx="1" fill="#6B7280"/>
                                <rect x="43" y="46" width="6" height="6" rx="1" fill="#10B981"/>
                            </svg>
                        </div>
                        <div class="icon-name">Calculator</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;calcGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#1F2937&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#374151&quot;/></linearGradient></defs><rect x=&quot;12&quot; y=&quot;8&quot; width=&quot;40&quot; height=&quot;48&quot; rx=&quot;6&quot; fill=&quot;url(#calcGrad)&quot;/><rect x=&quot;16&quot; y=&quot;12&quot; width=&quot;32&quot; height=&quot;12&quot; rx=&quot;2&quot; fill=&quot;#10B981&quot;/><text x=&quot;32&quot; y=&quot;21&quot; text-anchor=&quot;middle&quot; fill=&quot;white&quot; font-family=&quot;monospace&quot; font-size=&quot;8&quot;>123.45</text><rect x=&quot;16&quot; y=&quot;28&quot; width=&quot;6&quot; height=&quot;6&quot; rx=&quot;1&quot; fill=&quot;#6B7280&quot;/><rect x=&quot;25&quot; y=&quot;28&quot; width=&quot;6&quot; height=&quot;6&quot; rx=&quot;1&quot; fill=&quot;#6B7280&quot;/><rect x=&quot;34&quot; y=&quot;28&quot; width=&quot;6&quot; height=&quot;6&quot; rx=&quot;1&quot; fill=&quot;#6B7280&quot;/><rect x=&quot;43&quot; y=&quot;28&quot; width=&quot;6&quot; height=&quot;15&quot; rx=&quot;1&quot; fill=&quot;#EF4444&quot;/><rect x=&quot;16&quot; y=&quot;37&quot; width=&quot;6&quot; height=&quot;6&quot; rx=&quot;1&quot; fill=&quot;#6B7280&quot;/><rect x=&quot;25&quot; y=&quot;37&quot; width=&quot;6&quot; height=&quot;6&quot; rx=&quot;1&quot; fill=&quot;#6B7280&quot;/><rect x=&quot;34&quot; y=&quot;37&quot; width=&quot;6&quot; height=&quot;6&quot; rx=&quot;1&quot; fill=&quot;#6B7280&quot;/><rect x=&quot;16&quot; y=&quot;46&quot; width=&quot;15&quot; height=&quot;6&quot; rx=&quot;1&quot; fill=&quot;#3B82F6&quot;/><rect x=&quot;34&quot; y=&quot;46&quot; width=&quot;6&quot; height=&quot;6&quot; rx=&quot;1&quot; fill=&quot;#6B7280&quot;/><rect x=&quot;43&quot; y=&quot;46&quot; width=&quot;6&quot; height=&quot;6&quot; rx=&quot;1&quot; fill=&quot;#10B981&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Target Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="targetGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#EF4444"/>
                                        <stop offset="100%" style="stop-color:#DC2626"/>
                                    </linearGradient>
                                </defs>
                                <circle cx="32" cy="32" r="24" fill="url(#targetGrad)"/>
                                <circle cx="32" cy="32" r="18" fill="#FFFFFF"/>
                                <circle cx="32" cy="32" r="12" fill="url(#targetGrad)"/>
                                <circle cx="32" cy="32" r="6" fill="#FFFFFF"/>
                                <circle cx="32" cy="32" r="3" fill="url(#targetGrad)"/>
                                <path d="M32 8 L36 16 L32 24 L28 16 Z" fill="#FBBF24"/>
                            </svg>
                        </div>
                        <div class="icon-name">Target</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;targetGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#EF4444&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#DC2626&quot;/></linearGradient></defs><circle cx=&quot;32&quot; cy=&quot;32&quot; r=&quot;24&quot; fill=&quot;url(#targetGrad)&quot;/><circle cx=&quot;32&quot; cy=&quot;32&quot; r=&quot;18&quot; fill=&quot;#FFFFFF&quot;/><circle cx=&quot;32&quot; cy=&quot;32&quot; r=&quot;12&quot; fill=&quot;url(#targetGrad)&quot;/><circle cx=&quot;32&quot; cy=&quot;32&quot; r=&quot;6&quot; fill=&quot;#FFFFFF&quot;/><circle cx=&quot;32&quot; cy=&quot;32&quot; r=&quot;3&quot; fill=&quot;url(#targetGrad)&quot;/><path d=&quot;M32 8 L36 16 L32 24 L28 16 Z&quot; fill=&quot;#FBBF24&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Money Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="moneyGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#10B981"/>
                                        <stop offset="100%" style="stop-color:#059669"/>
                                    </linearGradient>
                                </defs>
                                <rect x="8" y="20" width="48" height="24" rx="4" fill="url(#moneyGrad)"/>
                                <rect x="12" y="24" width="40" height="16" rx="2" fill="#FFFFFF" opacity="0.2"/>
                                <circle cx="32" cy="32" r="8" fill="#FFFFFF" opacity="0.9"/>
                                <text x="32" y="36" text-anchor="middle" fill="#10B981" font-family="serif" font-size="12" font-weight="bold">$</text>
                                <circle cx="16" cy="28" r="2" fill="#FFFFFF" opacity="0.6"/>
                                <circle cx="48" cy="36" r="2" fill="#FFFFFF" opacity="0.6"/>
                                <rect x="20" y="12" width="24" height="4" rx="2" fill="#FBBF24"/>
                                <rect x="20" y="48" width="24" height="4" rx="2" fill="#FBBF24"/>
                            </svg>
                        </div>
                        <div class="icon-name">Money</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;moneyGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#10B981&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#059669&quot;/></linearGradient></defs><rect x=&quot;8&quot; y=&quot;20&quot; width=&quot;48&quot; height=&quot;24&quot; rx=&quot;4&quot; fill=&quot;url(#moneyGrad)&quot;/><rect x=&quot;12&quot; y=&quot;24&quot; width=&quot;40&quot; height=&quot;16&quot; rx=&quot;2&quot; fill=&quot;#FFFFFF&quot; opacity=&quot;0.2&quot;/><circle cx=&quot;32&quot; cy=&quot;32&quot; r=&quot;8&quot; fill=&quot;#FFFFFF&quot; opacity=&quot;0.9&quot;/><text x=&quot;32&quot; y=&quot;36&quot; text-anchor=&quot;middle&quot; fill=&quot;#10B981&quot; font-family=&quot;serif&quot; font-size=&quot;12&quot; font-weight=&quot;bold&quot;>$</text><circle cx=&quot;16&quot; cy=&quot;28&quot; r=&quot;2&quot; fill=&quot;#FFFFFF&quot; opacity=&quot;0.6&quot;/><circle cx=&quot;48&quot; cy=&quot;36&quot; r=&quot;2&quot; fill=&quot;#FFFFFF&quot; opacity=&quot;0.6&quot;/><rect x=&quot;20&quot; y=&quot;12&quot; width=&quot;24&quot; height=&quot;4&quot; rx=&quot;2&quot; fill=&quot;#FBBF24&quot;/><rect x=&quot;20&quot; y=&quot;48&quot; width=&quot;24&quot; height=&quot;4&quot; rx=&quot;2&quot; fill=&quot;#FBBF24&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Team Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="teamGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#8B5CF6"/>
                                        <stop offset="100%" style="stop-color:#7C3AED"/>
                                    </linearGradient>
                                    <linearGradient id="teamGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#3B82F6"/>
                                        <stop offset="100%" style="stop-color:#2563EB"/>
                                    </linearGradient>
                                    <linearGradient id="teamGrad3" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#10B981"/>
                                        <stop offset="100%" style="stop-color:#059669"/>
                                    </linearGradient>
                                </defs>
                                <circle cx="20" cy="20" r="8" fill="url(#teamGrad1)"/>
                                <circle cx="32" cy="16" r="10" fill="url(#teamGrad2)"/>
                                <circle cx="44" cy="20" r="8" fill="url(#teamGrad3)"/>
                                <ellipse cx="20" cy="44" rx="12" ry="8" fill="url(#teamGrad1)" opacity="0.8"/>
                                <ellipse cx="32" cy="48" rx="16" ry="10" fill="url(#teamGrad2)" opacity="0.8"/>
                                <ellipse cx="44" cy="44" rx="12" ry="8" fill="url(#teamGrad3)" opacity="0.8"/>
                            </svg>
                        </div>
                        <div class="icon-name">Team</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;teamGrad1&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#8B5CF6&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#7C3AED&quot;/></linearGradient><linearGradient id=&quot;teamGrad2&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#3B82F6&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#2563EB&quot;/></linearGradient><linearGradient id=&quot;teamGrad3&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#10B981&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#059669&quot;/></linearGradient></defs><circle cx=&quot;20&quot; cy=&quot;20&quot; r=&quot;8&quot; fill=&quot;url(#teamGrad1)&quot;/><circle cx=&quot;32&quot; cy=&quot;16&quot; r=&quot;10&quot; fill=&quot;url(#teamGrad2)&quot;/><circle cx=&quot;44&quot; cy=&quot;20&quot; r=&quot;8&quot; fill=&quot;url(#teamGrad3)&quot;/><ellipse cx=&quot;20&quot; cy=&quot;44&quot; rx=&quot;12&quot; ry=&quot;8&quot; fill=&quot;url(#teamGrad1)&quot; opacity=&quot;0.8&quot;/><ellipse cx=&quot;32&quot; cy=&quot;48&quot; rx=&quot;16&quot; ry=&quot;10&quot; fill=&quot;url(#teamGrad2)&quot; opacity=&quot;0.8&quot;/><ellipse cx=&quot;44&quot; cy=&quot;44&quot; rx=&quot;12&quot; ry=&quot;8&quot; fill=&quot;url(#teamGrad3)&quot; opacity=&quot;0.8&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Growth Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="growthGrad" x1="0%" y1="100%" x2="100%" y2="0%">
                                        <stop offset="0%" style="stop-color:#10B981"/>
                                        <stop offset="100%" style="stop-color:#34D399"/>
                                    </linearGradient>
                                </defs>
                                <path d="M8 48 Q16 40 24 44 Q32 36 40 40 Q48 28 56 32" stroke="url(#growthGrad)" stroke-width="4" fill="none" stroke-linecap="round"/>
                                <circle cx="8" cy="48" r="3" fill="#10B981"/>
                                <circle cx="24" cy="44" r="3" fill="#10B981"/>
                                <circle cx="40" cy="40" r="3" fill="#10B981"/>
                                <circle cx="56" cy="32" r="3" fill="#10B981"/>
                                <path d="M48 16 L56 8 L56 16 L48 16" fill="#10B981"/>
                                <path d="M48 20 L56 12 L56 20 L48 20" fill="#34D399"/>
                                <rect x="12" y="52" width="4" height="8" fill="#94A3B8"/>
                                <rect x="28" y="48" width="4" height="12" fill="#94A3B8"/>
                                <rect x="44" y="44" width="4" height="16" fill="#94A3B8"/>
                            </svg>
                        </div>
                        <div class="icon-name">Growth</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;growthGrad&quot; x1=&quot;0%&quot; y1=&quot;100%&quot; x2=&quot;100%&quot; y2=&quot;0%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#10B981&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#34D399&quot;/></linearGradient></defs><path d=&quot;M8 48 Q16 40 24 44 Q32 36 40 40 Q48 28 56 32&quot; stroke=&quot;url(#growthGrad)&quot; stroke-width=&quot;4&quot; fill=&quot;none&quot; stroke-linecap=&quot;round&quot;/><circle cx=&quot;8&quot; cy=&quot;48&quot; r=&quot;3&quot; fill=&quot;#10B981&quot;/><circle cx=&quot;24&quot; cy=&quot;44&quot; r=&quot;3&quot; fill=&quot;#10B981&quot;/><circle cx=&quot;40&quot; cy=&quot;40&quot; r=&quot;3&quot; fill=&quot;#10B981&quot;/><circle cx=&quot;56&quot; cy=&quot;32&quot; r=&quot;3&quot; fill=&quot;#10B981&quot;/><path d=&quot;M48 16 L56 8 L56 16 L48 16&quot; fill=&quot;#10B981&quot;/><path d=&quot;M48 20 L56 12 L56 20 L48 20&quot; fill=&quot;#34D399&quot;/><rect x=&quot;12&quot; y=&quot;52&quot; width=&quot;4&quot; height=&quot;8&quot; fill=&quot;#94A3B8&quot;/><rect x=&quot;28&quot; y=&quot;48&quot; width=&quot;4&quot; height=&quot;12&quot; fill=&quot;#94A3B8&quot;/><rect x=&quot;44&quot; y=&quot;44&quot; width=&quot;4&quot; height=&quot;16&quot; fill=&quot;#94A3B8&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Strategy Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="strategyGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#6366F1"/>
                                        <stop offset="100%" style="stop-color:#4F46E5"/>
                                    </linearGradient>
                                </defs>
                                <rect x="8" y="8" width="48" height="48" rx="6" fill="url(#strategyGrad)"/>
                                <rect x="12" y="12" width="40" height="40" rx="4" fill="#FFFFFF" opacity="0.9"/>
                                <circle cx="20" cy="20" r="4" fill="#EF4444"/>
                                <circle cx="32" cy="28" r="4" fill="#3B82F6"/>
                                <circle cx="44" cy="36" r="4" fill="#10B981"/>
                                <circle cx="20" cy="44" r="4" fill="#F59E0B"/>
                                <line x1="20" y1="20" x2="32" y2="28" stroke="#94A3B8" stroke-width="2"/>
                                <line x1="32" y1="28" x2="44" y2="36" stroke="#94A3B8" stroke-width="2"/>
                                <line x1="20" y1="44" x2="32" y2="28" stroke="#94A3B8" stroke-width="2" stroke-dasharray="3,3"/>
                                <rect x="36" y="16" width="12" height="8" rx="2" fill="#FBBF24" opacity="0.8"/>
                                <text x="42" y="21" text-anchor="middle" fill="white" font-size="6" font-weight="bold">PLAN</text>
                            </svg>
                        </div>
                        <div class="icon-name">Strategy</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;strategyGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#6366F1&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#4F46E5&quot;/></linearGradient></defs><rect x=&quot;8&quot; y=&quot;8&quot; width=&quot;48&quot; height=&quot;48&quot; rx=&quot;6&quot; fill=&quot;url(#strategyGrad)&quot;/><rect x=&quot;12&quot; y=&quot;12&quot; width=&quot;40&quot; height=&quot;40&quot; rx=&quot;4&quot; fill=&quot;#FFFFFF&quot; opacity=&quot;0.9&quot;/><circle cx=&quot;20&quot; cy=&quot;20&quot; r=&quot;4&quot; fill=&quot;#EF4444&quot;/><circle cx=&quot;32&quot; cy=&quot;28&quot; r=&quot;4&quot; fill=&quot;#3B82F6&quot;/><circle cx=&quot;44&quot; cy=&quot;36&quot; r=&quot;4&quot; fill=&quot;#10B981&quot;/><circle cx=&quot;20&quot; cy=&quot;44&quot; r=&quot;4&quot; fill=&quot;#F59E0B&quot;/><line x1=&quot;20&quot; y1=&quot;20&quot; x2=&quot;32&quot; y2=&quot;28&quot; stroke=&quot;#94A3B8&quot; stroke-width=&quot;2&quot;/><line x1=&quot;32&quot; y1=&quot;28&quot; x2=&quot;44&quot; y2=&quot;36&quot; stroke=&quot;#94A3B8&quot; stroke-width=&quot;2&quot;/><line x1=&quot;20&quot; y1=&quot;44&quot; x2=&quot;32&quot; y2=&quot;28&quot; stroke=&quot;#94A3B8&quot; stroke-width=&quot;2&quot; stroke-dasharray=&quot;3,3&quot;/><rect x=&quot;36&quot; y=&quot;16&quot; width=&quot;12&quot; height=&quot;8&quot; rx=&quot;2&quot; fill=&quot;#FBBF24&quot; opacity=&quot;0.8&quot;/><text x=&quot;42&quot; y=&quot;21&quot; text-anchor=&quot;middle&quot; fill=&quot;white&quot; font-size=&quot;6&quot; font-weight=&quot;bold&quot;>PLAN</text></svg>')">Copy SVG</button>
                    </div>
                </div>
            </section>

            <!-- Vehicle Icons Category -->
            <section class="category-section">
                <div class="category-header">
                    <h2 class="category-title">Vehicle Icons</h2>
                    <span class="category-count">10 icons</span>
                </div>
                <div class="icon-grid">
                    <!-- Car Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="carGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#EF4444"/>
                                        <stop offset="100%" style="stop-color:#DC2626"/>
                                    </linearGradient>
                                </defs>
                                <ellipse cx="32" cy="44" rx="28" ry="8" fill="#374151" opacity="0.3"/>
                                <rect x="8" y="28" width="48" height="16" rx="8" fill="url(#carGrad)"/>
                                <rect x="12" y="20" width="40" height="12" rx="6" fill="url(#carGrad)"/>
                                <rect x="16" y="22" width="12" height="8" rx="2" fill="#60A5FA" opacity="0.8"/>
                                <rect x="36" y="22" width="12" height="8" rx="2" fill="#60A5FA" opacity="0.8"/>
                                <circle cx="18" cy="44" r="6" fill="#1F2937"/>
                                <circle cx="46" cy="44" r="6" fill="#1F2937"/>
                                <circle cx="18" cy="44" r="4" fill="#6B7280"/>
                                <circle cx="46" cy="44" r="4" fill="#6B7280"/>
                                <rect x="28" y="32" width="8" height="4" rx="2" fill="#FBBF24"/>
                            </svg>
                        </div>
                        <div class="icon-name">Car</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;carGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#EF4444&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#DC2626&quot;/></linearGradient></defs><ellipse cx=&quot;32&quot; cy=&quot;44&quot; rx=&quot;28&quot; ry=&quot;8&quot; fill=&quot;#374151&quot; opacity=&quot;0.3&quot;/><rect x=&quot;8&quot; y=&quot;28&quot; width=&quot;48&quot; height=&quot;16&quot; rx=&quot;8&quot; fill=&quot;url(#carGrad)&quot;/><rect x=&quot;12&quot; y=&quot;20&quot; width=&quot;40&quot; height=&quot;12&quot; rx=&quot;6&quot; fill=&quot;url(#carGrad)&quot;/><rect x=&quot;16&quot; y=&quot;22&quot; width=&quot;12&quot; height=&quot;8&quot; rx=&quot;2&quot; fill=&quot;#60A5FA&quot; opacity=&quot;0.8&quot;/><rect x=&quot;36&quot; y=&quot;22&quot; width=&quot;12&quot; height=&quot;8&quot; rx=&quot;2&quot; fill=&quot;#60A5FA&quot; opacity=&quot;0.8&quot;/><circle cx=&quot;18&quot; cy=&quot;44&quot; r=&quot;6&quot; fill=&quot;#1F2937&quot;/><circle cx=&quot;46&quot; cy=&quot;44&quot; r=&quot;6&quot; fill=&quot;#1F2937&quot;/><circle cx=&quot;18&quot; cy=&quot;44&quot; r=&quot;4&quot; fill=&quot;#6B7280&quot;/><circle cx=&quot;46&quot; cy=&quot;44&quot; r=&quot;4&quot; fill=&quot;#6B7280&quot;/><rect x=&quot;28&quot; y=&quot;32&quot; width=&quot;8&quot; height=&quot;4&quot; rx=&quot;2&quot; fill=&quot;#FBBF24&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Truck Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="truckGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#3B82F6"/>
                                        <stop offset="100%" style="stop-color:#2563EB"/>
                                    </linearGradient>
                                </defs>
                                <ellipse cx="32" cy="48" rx="30" ry="6" fill="#374151" opacity="0.3"/>
                                <rect x="8" y="24" width="24" height="20" rx="4" fill="url(#truckGrad)"/>
                                <rect x="32" y="32" width="20" height="12" rx="4" fill="url(#truckGrad)"/>
                                <rect x="10" y="16" width="20" height="12" rx="2" fill="url(#truckGrad)"/>
                                <rect x="12" y="18" width="8" height="8" rx="1" fill="#60A5FA" opacity="0.8"/>
                                <rect x="22" y="18" width="6" height="8" rx="1" fill="#60A5FA" opacity="0.8"/>
                                <circle cx="18" cy="44" r="6" fill="#1F2937"/>
                                <circle cx="42" cy="44" r="6" fill="#1F2937"/>
                                <circle cx="18" cy="44" r="4" fill="#6B7280"/>
                                <circle cx="42" cy="44" r="4" fill="#6B7280"/>
                                <rect x="34" y="36" width="16" height="6" rx="1" fill="#FBBF24"/>
                            </svg>
                        </div>
                        <div class="icon-name">Truck</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;truckGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#3B82F6&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#2563EB&quot;/></linearGradient></defs><ellipse cx=&quot;32&quot; cy=&quot;48&quot; rx=&quot;30&quot; ry=&quot;6&quot; fill=&quot;#374151&quot; opacity=&quot;0.3&quot;/><rect x=&quot;8&quot; y=&quot;24&quot; width=&quot;24&quot; height=&quot;20&quot; rx=&quot;4&quot; fill=&quot;url(#truckGrad)&quot;/><rect x=&quot;32&quot; y=&quot;32&quot; width=&quot;20&quot; height=&quot;12&quot; rx=&quot;4&quot; fill=&quot;url(#truckGrad)&quot;/><rect x=&quot;10&quot; y=&quot;16&quot; width=&quot;20&quot; height=&quot;12&quot; rx=&quot;2&quot; fill=&quot;url(#truckGrad)&quot;/><rect x=&quot;12&quot; y=&quot;18&quot; width=&quot;8&quot; height=&quot;8&quot; rx=&quot;1&quot; fill=&quot;#60A5FA&quot; opacity=&quot;0.8&quot;/><rect x=&quot;22&quot; y=&quot;18&quot; width=&quot;6&quot; height=&quot;8&quot; rx=&quot;1&quot; fill=&quot;#60A5FA&quot; opacity=&quot;0.8&quot;/><circle cx=&quot;18&quot; cy=&quot;44&quot; r=&quot;6&quot; fill=&quot;#1F2937&quot;/><circle cx=&quot;42&quot; cy=&quot;44&quot; r=&quot;6&quot; fill=&quot;#1F2937&quot;/><circle cx=&quot;18&quot; cy=&quot;44&quot; r=&quot;4&quot; fill=&quot;#6B7280&quot;/><circle cx=&quot;42&quot; cy=&quot;44&quot; r=&quot;4&quot; fill=&quot;#6B7280&quot;/><rect x=&quot;34&quot; y=&quot;36&quot; width=&quot;16&quot; height=&quot;6&quot; rx=&quot;1&quot; fill=&quot;#FBBF24&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Airplane Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="planeGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#60A5FA"/>
                                        <stop offset="100%" style="stop-color:#3B82F6"/>
                                    </linearGradient>
                                </defs>
                                <ellipse cx="32" cy="50" rx="24" ry="4" fill="#374151" opacity="0.2"/>
                                <rect x="28" y="16" width="8" height="32" rx="4" fill="url(#planeGrad)"/>
                                <ellipse cx="32" cy="20" rx="6" ry="8" fill="url(#planeGrad)"/>
                                <rect x="12" y="28" width="40" height="6" rx="3" fill="url(#planeGrad)"/>
                                <rect x="20" y="38" width="24" height="4" rx="2" fill="url(#planeGrad)"/>
                                <circle cx="32" cy="18" r="3" fill="#FBBF24"/>
                                <rect x="30" y="44" width="4" height="8" rx="2" fill="#6B7280"/>
                                <circle cx="18" cy="31" r="2" fill="#EF4444"/>
                                <circle cx="46" cy="31" r="2" fill="#EF4444"/>
                                <path d="M32 8 L36 16 L32 12 L28 16 Z" fill="#FBBF24"/>
                            </svg>
                        </div>
                        <div class="icon-name">Airplane</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;planeGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#60A5FA&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#3B82F6&quot;/></linearGradient></defs><ellipse cx=&quot;32&quot; cy=&quot;50&quot; rx=&quot;24&quot; ry=&quot;4&quot; fill=&quot;#374151&quot; opacity=&quot;0.2&quot;/><rect x=&quot;28&quot; y=&quot;16&quot; width=&quot;8&quot; height=&quot;32&quot; rx=&quot;4&quot; fill=&quot;url(#planeGrad)&quot;/><ellipse cx=&quot;32&quot; cy=&quot;20&quot; rx=&quot;6&quot; ry=&quot;8&quot; fill=&quot;url(#planeGrad)&quot;/><rect x=&quot;12&quot; y=&quot;28&quot; width=&quot;40&quot; height=&quot;6&quot; rx=&quot;3&quot; fill=&quot;url(#planeGrad)&quot;/><rect x=&quot;20&quot; y=&quot;38&quot; width=&quot;24&quot; height=&quot;4&quot; rx=&quot;2&quot; fill=&quot;url(#planeGrad)&quot;/><circle cx=&quot;32&quot; cy=&quot;18&quot; r=&quot;3&quot; fill=&quot;#FBBF24&quot;/><rect x=&quot;30&quot; y=&quot;44&quot; width=&quot;4&quot; height=&quot;8&quot; rx=&quot;2&quot; fill=&quot;#6B7280&quot;/><circle cx=&quot;18&quot; cy=&quot;31&quot; r=&quot;2&quot; fill=&quot;#EF4444&quot;/><circle cx=&quot;46&quot; cy=&quot;31&quot; r=&quot;2&quot; fill=&quot;#EF4444&quot;/><path d=&quot;M32 8 L36 16 L32 12 L28 16 Z&quot; fill=&quot;#FBBF24&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Motorcycle Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="bikeGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#F59E0B"/>
                                        <stop offset="100%" style="stop-color:#D97706"/>
                                    </linearGradient>
                                </defs>
                                <ellipse cx="32" cy="50" rx="28" ry="4" fill="#374151" opacity="0.2"/>
                                <circle cx="16" cy="40" r="10" fill="#1F2937"/>
                                <circle cx="48" cy="40" r="10" fill="#1F2937"/>
                                <circle cx="16" cy="40" r="7" fill="#6B7280"/>
                                <circle cx="48" cy="40" r="7" fill="#6B7280"/>
                                <rect x="24" y="28" width="16" height="6" rx="3" fill="url(#bikeGrad)"/>
                                <rect x="28" y="20" width="8" height="12" rx="2" fill="url(#bikeGrad)"/>
                                <circle cx="32" cy="22" r="3" fill="#EF4444"/>
                                <line x1="26" y1="31" x2="16" y2="40" stroke="url(#bikeGrad)" stroke-width="3"/>
                                <line x1="38" y1="31" x2="48" y2="40" stroke="url(#bikeGrad)" stroke-width="3"/>
                                <rect x="30" y="16" width="4" height="8" rx="2" fill="#6B7280"/>
                            </svg>
                        </div>
                        <div class="icon-name">Motorcycle</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;bikeGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#F59E0B&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#D97706&quot;/></linearGradient></defs><ellipse cx=&quot;32&quot; cy=&quot;50&quot; rx=&quot;28&quot; ry=&quot;4&quot; fill=&quot;#374151&quot; opacity=&quot;0.2&quot;/><circle cx=&quot;16&quot; cy=&quot;40&quot; r=&quot;10&quot; fill=&quot;#1F2937&quot;/><circle cx=&quot;48&quot; cy=&quot;40&quot; r=&quot;10&quot; fill=&quot;#1F2937&quot;/><circle cx=&quot;16&quot; cy=&quot;40&quot; r=&quot;7&quot; fill=&quot;#6B7280&quot;/><circle cx=&quot;48&quot; cy=&quot;40&quot; r=&quot;7&quot; fill=&quot;#6B7280&quot;/><rect x=&quot;24&quot; y=&quot;28&quot; width=&quot;16&quot; height=&quot;6&quot; rx=&quot;3&quot; fill=&quot;url(#bikeGrad)&quot;/><rect x=&quot;28&quot; y=&quot;20&quot; width=&quot;8&quot; height=&quot;12&quot; rx=&quot;2&quot; fill=&quot;url(#bikeGrad)&quot;/><circle cx=&quot;32&quot; cy=&quot;22&quot; r=&quot;3&quot; fill=&quot;#EF4444&quot;/><line x1=&quot;26&quot; y1=&quot;31&quot; x2=&quot;16&quot; y2=&quot;40&quot; stroke=&quot;url(#bikeGrad)&quot; stroke-width=&quot;3&quot;/><line x1=&quot;38&quot; y1=&quot;31&quot; x2=&quot;48&quot; y2=&quot;40&quot; stroke=&quot;url(#bikeGrad)&quot; stroke-width=&quot;3&quot;/><rect x=&quot;30&quot; y=&quot;16&quot; width=&quot;4&quot; height=&quot;8&quot; rx=&quot;2&quot; fill=&quot;#6B7280&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Bicycle Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="cycleGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#10B981"/>
                                        <stop offset="100%" style="stop-color:#059669"/>
                                    </linearGradient>
                                </defs>
                                <ellipse cx="32" cy="50" rx="26" ry="3" fill="#374151" opacity="0.2"/>
                                <circle cx="16" cy="40" r="8" fill="#1F2937"/>
                                <circle cx="48" cy="40" r="8" fill="#1F2937"/>
                                <circle cx="16" cy="40" r="6" fill="#6B7280"/>
                                <circle cx="48" cy="40" r="6" fill="#6B7280"/>
                                <line x1="16" y1="40" x2="32" y2="24" stroke="url(#cycleGrad)" stroke-width="2"/>
                                <line x1="32" y1="24" x2="48" y2="40" stroke="url(#cycleGrad)" stroke-width="2"/>
                                <line x1="24" y1="32" x2="40" y2="32" stroke="url(#cycleGrad)" stroke-width="2"/>
                                <circle cx="32" cy="24" r="3" fill="url(#cycleGrad)"/>
                                <rect x="30" y="16" width="4" height="12" rx="2" fill="url(#cycleGrad)"/>
                                <circle cx="32" cy="18" r="2" fill="#FBBF24"/>
                                <rect x="28" y="30" width="8" height="4" rx="2" fill="#6B7280"/>
                            </svg>
                        </div>
                        <div class="icon-name">Bicycle</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;cycleGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#10B981&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#059669&quot;/></linearGradient></defs><ellipse cx=&quot;32&quot; cy=&quot;50&quot; rx=&quot;26&quot; ry=&quot;3&quot; fill=&quot;#374151&quot; opacity=&quot;0.2&quot;/><circle cx=&quot;16&quot; cy=&quot;40&quot; r=&quot;8&quot; fill=&quot;#1F2937&quot;/><circle cx=&quot;48&quot; cy=&quot;40&quot; r=&quot;8&quot; fill=&quot;#1F2937&quot;/><circle cx=&quot;16&quot; cy=&quot;40&quot; r=&quot;6&quot; fill=&quot;#6B7280&quot;/><circle cx=&quot;48&quot; cy=&quot;40&quot; r=&quot;6&quot; fill=&quot;#6B7280&quot;/><line x1=&quot;16&quot; y1=&quot;40&quot; x2=&quot;32&quot; y2=&quot;24&quot; stroke=&quot;url(#cycleGrad)&quot; stroke-width=&quot;2&quot;/><line x1=&quot;32&quot; y1=&quot;24&quot; x2=&quot;48&quot; y2=&quot;40&quot; stroke=&quot;url(#cycleGrad)&quot; stroke-width=&quot;2&quot;/><line x1=&quot;24&quot; y1=&quot;32&quot; x2=&quot;40&quot; y2=&quot;32&quot; stroke=&quot;url(#cycleGrad)&quot; stroke-width=&quot;2&quot;/><circle cx=&quot;32&quot; cy=&quot;24&quot; r=&quot;3&quot; fill=&quot;url(#cycleGrad)&quot;/><rect x=&quot;30&quot; y=&quot;16&quot; width=&quot;4&quot; height=&quot;12&quot; rx=&quot;2&quot; fill=&quot;url(#cycleGrad)&quot;/><circle cx=&quot;32&quot; cy=&quot;18&quot; r=&quot;2&quot; fill=&quot;#FBBF24&quot;/><rect x=&quot;28&quot; y=&quot;30&quot; width=&quot;8&quot; height=&quot;4&quot; rx=&quot;2&quot; fill=&quot;#6B7280&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Ship Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="shipGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#8B5CF6"/>
                                        <stop offset="100%" style="stop-color:#7C3AED"/>
                                    </linearGradient>
                                </defs>
                                <ellipse cx="32" cy="52" rx="28" ry="4" fill="#3B82F6" opacity="0.3"/>
                                <path d="M12 40 L52 40 L48 48 L16 48 Z" fill="url(#shipGrad)"/>
                                <rect x="28" y="16" width="8" height="28" rx="2" fill="#6B7280"/>
                                <rect x="20" y="20" width="24" height="20" rx="4" fill="url(#shipGrad)"/>
                                <rect x="24" y="24" width="6" height="8" rx="1" fill="#60A5FA" opacity="0.8"/>
                                <rect x="34" y="24" width="6" height="8" rx="1" fill="#60A5FA" opacity="0.8"/>
                                <rect x="29" y="34" width="6" height="4" rx="1" fill="#FBBF24"/>
                                <path d="M32 8 L36 16 L32 20 L28 16 Z" fill="#EF4444"/>
                                <line x1="8" y1="48" x2="56" y2="48" stroke="#3B82F6" stroke-width="2"/>
                                <path d="M8 48 Q12 52 16 48 Q20 52 24 48 Q28 52 32 48 Q36 52 40 48 Q44 52 48 48 Q52 52 56 48" stroke="#60A5FA" stroke-width="1" fill="none"/>
                            </svg>
                        </div>
                        <div class="icon-name">Ship</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;shipGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#8B5CF6&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#7C3AED&quot;/></linearGradient></defs><ellipse cx=&quot;32&quot; cy=&quot;52&quot; rx=&quot;28&quot; ry=&quot;4&quot; fill=&quot;#3B82F6&quot; opacity=&quot;0.3&quot;/><path d=&quot;M12 40 L52 40 L48 48 L16 48 Z&quot; fill=&quot;url(#shipGrad)&quot;/><rect x=&quot;28&quot; y=&quot;16&quot; width=&quot;8&quot; height=&quot;28&quot; rx=&quot;2&quot; fill=&quot;#6B7280&quot;/><rect x=&quot;20&quot; y=&quot;20&quot; width=&quot;24&quot; height=&quot;20&quot; rx=&quot;4&quot; fill=&quot;url(#shipGrad)&quot;/><rect x=&quot;24&quot; y=&quot;24&quot; width=&quot;6&quot; height=&quot;8&quot; rx=&quot;1&quot; fill=&quot;#60A5FA&quot; opacity=&quot;0.8&quot;/><rect x=&quot;34&quot; y=&quot;24&quot; width=&quot;6&quot; height=&quot;8&quot; rx=&quot;1&quot; fill=&quot;#60A5FA&quot; opacity=&quot;0.8&quot;/><rect x=&quot;29&quot; y=&quot;34&quot; width=&quot;6&quot; height=&quot;4&quot; rx=&quot;1&quot; fill=&quot;#FBBF24&quot;/><path d=&quot;M32 8 L36 16 L32 20 L28 16 Z&quot; fill=&quot;#EF4444&quot;/><line x1=&quot;8&quot; y1=&quot;48&quot; x2=&quot;56&quot; y2=&quot;48&quot; stroke=&quot;#3B82F6&quot; stroke-width=&quot;2&quot;/><path d=&quot;M8 48 Q12 52 16 48 Q20 52 24 48 Q28 52 32 48 Q36 52 40 48 Q44 52 48 48 Q52 52 56 48&quot; stroke=&quot;#60A5FA&quot; stroke-width=&quot;1&quot; fill=&quot;none&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Train Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="trainGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#DC2626"/>
                                        <stop offset="100%" style="stop-color:#B91C1C"/>
                                    </linearGradient>
                                </defs>
                                <ellipse cx="32" cy="50" rx="26" ry="4" fill="#374151" opacity="0.2"/>
                                <rect x="12" y="24" width="40" height="20" rx="6" fill="url(#trainGrad)"/>
                                <rect x="8" y="20" width="8" height="8" rx="4" fill="url(#trainGrad)"/>
                                <rect x="16" y="28" width="8" height="8" rx="2" fill="#60A5FA" opacity="0.8"/>
                                <rect x="28" y="28" width="8" height="8" rx="2" fill="#60A5FA" opacity="0.8"/>
                                <rect x="40" y="28" width="8" height="8" rx="2" fill="#60A5FA" opacity="0.8"/>
                                <circle cx="20" cy="44" r="4" fill="#1F2937"/>
                                <circle cx="32" cy="44" r="4" fill="#1F2937"/>
                                <circle cx="44" cy="44" r="4" fill="#1F2937"/>
                                <circle cx="20" cy="44" r="2" fill="#6B7280"/>
                                <circle cx="32" cy="44" r="2" fill="#6B7280"/>
                                <circle cx="44" cy="44" r="2" fill="#6B7280"/>
                                <rect x="24" y="38" width="16" height="4" rx="2" fill="#FBBF24"/>
                                <circle cx="12" cy="24" r="2" fill="#FBBF24"/>
                            </svg>
                        </div>
                        <div class="icon-name">Train</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;trainGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#DC2626&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#B91C1C&quot;/></linearGradient></defs><ellipse cx=&quot;32&quot; cy=&quot;50&quot; rx=&quot;26&quot; ry=&quot;4&quot; fill=&quot;#374151&quot; opacity=&quot;0.2&quot;/><rect x=&quot;12&quot; y=&quot;24&quot; width=&quot;40&quot; height=&quot;20&quot; rx=&quot;6&quot; fill=&quot;url(#trainGrad)&quot;/><rect x=&quot;8&quot; y=&quot;20&quot; width=&quot;8&quot; height=&quot;8&quot; rx=&quot;4&quot; fill=&quot;url(#trainGrad)&quot;/><rect x=&quot;16&quot; y=&quot;28&quot; width=&quot;8&quot; height=&quot;8&quot; rx=&quot;2&quot; fill=&quot;#60A5FA&quot; opacity=&quot;0.8&quot;/><rect x=&quot;28&quot; y=&quot;28&quot; width=&quot;8&quot; height=&quot;8&quot; rx=&quot;2&quot; fill=&quot;#60A5FA&quot; opacity=&quot;0.8&quot;/><rect x=&quot;40&quot; y=&quot;28&quot; width=&quot;8&quot; height=&quot;8&quot; rx=&quot;2&quot; fill=&quot;#60A5FA&quot; opacity=&quot;0.8&quot;/><circle cx=&quot;20&quot; cy=&quot;44&quot; r=&quot;4&quot; fill=&quot;#1F2937&quot;/><circle cx=&quot;32&quot; cy=&quot;44&quot; r=&quot;4&quot; fill=&quot;#1F2937&quot;/><circle cx=&quot;44&quot; cy=&quot;44&quot; r=&quot;4&quot; fill=&quot;#1F2937&quot;/><circle cx=&quot;20&quot; cy=&quot;44&quot; r=&quot;2&quot; fill=&quot;#6B7280&quot;/><circle cx=&quot;32&quot; cy=&quot;44&quot; r=&quot;2&quot; fill=&quot;#6B7280&quot;/><circle cx=&quot;44&quot; cy=&quot;44&quot; r=&quot;2&quot; fill=&quot;#6B7280&quot;/><rect x=&quot;24&quot; y=&quot;38&quot; width=&quot;16&quot; height=&quot;4&quot; rx=&quot;2&quot; fill=&quot;#FBBF24&quot;/><circle cx=&quot;12&quot; cy=&quot;24&quot; r=&quot;2&quot; fill=&quot;#FBBF24&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Bus Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="busGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#FBBF24"/>
                                        <stop offset="100%" style="stop-color:#F59E0B"/>
                                    </linearGradient>
                                </defs>
                                <ellipse cx="32" cy="50" rx="28" ry="4" fill="#374151" opacity="0.2"/>
                                <rect x="12" y="16" width="40" height="28" rx="6" fill="url(#busGrad)"/>
                                <rect x="16" y="20" width="6" height="6" rx="1" fill="#60A5FA" opacity="0.8"/>
                                <rect x="24" y="20" width="6" height="6" rx="1" fill="#60A5FA" opacity="0.8"/>
                                <rect x="34" y="20" width="6" height="6" rx="1" fill="#60A5FA" opacity="0.8"/>
                                <rect x="42" y="20" width="6" height="6" rx="1" fill="#60A5FA" opacity="0.8"/>
                                <rect x="16" y="28" width="32" height="8" rx="2" fill="#FFFFFF" opacity="0.9"/>
                                <circle cx="20" cy="44" r="5" fill="#1F2937"/>
                                <circle cx="44" cy="44" r="5" fill="#1F2937"/>
                                <circle cx="20" cy="44" r="3" fill="#6B7280"/>
                                <circle cx="44" cy="44" r="3" fill="#6B7280"/>
                                <rect x="28" y="36" width="8" height="4" rx="2" fill="#EF4444"/>
                                <rect x="50" y="24" width="2" height="8" rx="1" fill="#FFFFFF"/>
                            </svg>
                        </div>
                        <div class="icon-name">Bus</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;busGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#FBBF24&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#F59E0B&quot;/></linearGradient></defs><ellipse cx=&quot;32&quot; cy=&quot;50&quot; rx=&quot;28&quot; ry=&quot;4&quot; fill=&quot;#374151&quot; opacity=&quot;0.2&quot;/><rect x=&quot;12&quot; y=&quot;16&quot; width=&quot;40&quot; height=&quot;28&quot; rx=&quot;6&quot; fill=&quot;url(#busGrad)&quot;/><rect x=&quot;16&quot; y=&quot;20&quot; width=&quot;6&quot; height=&quot;6&quot; rx=&quot;1&quot; fill=&quot;#60A5FA&quot; opacity=&quot;0.8&quot;/><rect x=&quot;24&quot; y=&quot;20&quot; width=&quot;6&quot; height=&quot;6&quot; rx=&quot;1&quot; fill=&quot;#60A5FA&quot; opacity=&quot;0.8&quot;/><rect x=&quot;34&quot; y=&quot;20&quot; width=&quot;6&quot; height=&quot;6&quot; rx=&quot;1&quot; fill=&quot;#60A5FA&quot; opacity=&quot;0.8&quot;/><rect x=&quot;42&quot; y=&quot;20&quot; width=&quot;6&quot; height=&quot;6&quot; rx=&quot;1&quot; fill=&quot;#60A5FA&quot; opacity=&quot;0.8&quot;/><rect x=&quot;16&quot; y=&quot;28&quot; width=&quot;32&quot; height=&quot;8&quot; rx=&quot;2&quot; fill=&quot;#FFFFFF&quot; opacity=&quot;0.9&quot;/><circle cx=&quot;20&quot; cy=&quot;44&quot; r=&quot;5&quot; fill=&quot;#1F2937&quot;/><circle cx=&quot;44&quot; cy=&quot;44&quot; r=&quot;5&quot; fill=&quot;#1F2937&quot;/><circle cx=&quot;20&quot; cy=&quot;44&quot; r=&quot;3&quot; fill=&quot;#6B7280&quot;/><circle cx=&quot;44&quot; cy=&quot;44&quot; r=&quot;3&quot; fill=&quot;#6B7280&quot;/><rect x=&quot;28&quot; y=&quot;36&quot; width=&quot;8&quot; height=&quot;4&quot; rx=&quot;2&quot; fill=&quot;#EF4444&quot;/><rect x=&quot;50&quot; y=&quot;24&quot; width=&quot;2&quot; height=&quot;8&quot; rx=&quot;1&quot; fill=&quot;#FFFFFF&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Helicopter Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="heliGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#10B981"/>
                                        <stop offset="100%" style="stop-color:#059669"/>
                                    </linearGradient>
                                </defs>
                                <ellipse cx="32" cy="52" rx="24" ry="3" fill="#374151" opacity="0.2"/>
                                <ellipse cx="32" cy="20" rx="20" ry="2" fill="#6B7280" opacity="0.6"/>
                                <rect x="20" y="28" width="24" height="12" rx="6" fill="url(#heliGrad)"/>
                                <rect x="24" y="30" width="16" height="8" rx="2" fill="#60A5FA" opacity="0.8"/>
                                <rect x="30" y="40" width="4" height="8" rx="2" fill="#6B7280"/>
                                <circle cx="26" cy="48" r="3" fill="#1F2937"/>
                                <circle cx="38" cy="48" r="3" fill="#1F2937"/>
                                <rect x="44" y="32" width="8" height="2" rx="1" fill="url(#heliGrad)"/>
                                <circle cx="52" cy="33" r="4" fill="#6B7280" opacity="0.6"/>
                                <rect x="28" y="12" width="8" height="8" rx="2" fill="#6B7280"/>
                                <line x1="12" y1="20" x2="52" y2="20" stroke="#6B7280" stroke-width="1" opacity="0.6"/>
                                <rect x="46" y="16" width="8" height="2" rx="1" fill="#EF4444"/>
                            </svg>
                        </div>
                        <div class="icon-name">Helicopter</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;heliGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#10B981&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#059669&quot;/></linearGradient></defs><ellipse cx=&quot;32&quot; cy=&quot;52&quot; rx=&quot;24&quot; ry=&quot;3&quot; fill=&quot;#374151&quot; opacity=&quot;0.2&quot;/><ellipse cx=&quot;32&quot; cy=&quot;20&quot; rx=&quot;20&quot; ry=&quot;2&quot; fill=&quot;#6B7280&quot; opacity=&quot;0.6&quot;/><rect x=&quot;20&quot; y=&quot;28&quot; width=&quot;24&quot; height=&quot;12&quot; rx=&quot;6&quot; fill=&quot;url(#heliGrad)&quot;/><rect x=&quot;24&quot; y=&quot;30&quot; width=&quot;16&quot; height=&quot;8&quot; rx=&quot;2&quot; fill=&quot;#60A5FA&quot; opacity=&quot;0.8&quot;/><rect x=&quot;30&quot; y=&quot;40&quot; width=&quot;4&quot; height=&quot;8&quot; rx=&quot;2&quot; fill=&quot;#6B7280&quot;/><circle cx=&quot;26&quot; cy=&quot;48&quot; r=&quot;3&quot; fill=&quot;#1F2937&quot;/><circle cx=&quot;38&quot; cy=&quot;48&quot; r=&quot;3&quot; fill=&quot;#1F2937&quot;/><rect x=&quot;44&quot; y=&quot;32&quot; width=&quot;8&quot; height=&quot;2&quot; rx=&quot;1&quot; fill=&quot;url(#heliGrad)&quot;/><circle cx=&quot;52&quot; cy=&quot;33&quot; r=&quot;4&quot; fill=&quot;#6B7280&quot; opacity=&quot;0.6&quot;/><rect x=&quot;28&quot; y=&quot;12&quot; width=&quot;8&quot; height=&quot;8&quot; rx=&quot;2&quot; fill=&quot;#6B7280&quot;/><line x1=&quot;12&quot; y1=&quot;20&quot; x2=&quot;52&quot; y2=&quot;20&quot; stroke=&quot;#6B7280&quot; stroke-width=&quot;1&quot; opacity=&quot;0.6&quot;/><rect x=&quot;46&quot; y=&quot;16&quot; width=&quot;8&quot; height=&quot;2&quot; rx=&quot;1&quot; fill=&quot;#EF4444&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Scooter Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="scooterGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#8B5CF6"/>
                                        <stop offset="100%" style="stop-color:#7C3AED"/>
                                    </linearGradient>
                                </defs>
                                <ellipse cx="32" cy="50" rx="26" ry="3" fill="#374151" opacity="0.2"/>
                                <circle cx="16" cy="40" r="8" fill="#1F2937"/>
                                <circle cx="48" cy="40" r="6" fill="#1F2937"/>
                                <circle cx="16" cy="40" r="6" fill="#6B7280"/>
                                <circle cx="48" cy="40" r="4" fill="#6B7280"/>
                                <rect x="24" y="32" width="16" height="4" rx="2" fill="url(#scooterGrad)"/>
                                <rect x="32" y="20" width="4" height="16" rx="2" fill="url(#scooterGrad)"/>
                                <rect x="28" y="16" width="12" height="8" rx="4" fill="url(#scooterGrad)"/>
                                <circle cx="34" cy="20" r="2" fill="#FBBF24"/>
                                <line x1="24" y1="34" x2="16" y2="40" stroke="url(#scooterGrad)" stroke-width="2"/>
                                <line x1="40" y1="34" x2="48" y2="40" stroke="url(#scooterGrad)" stroke-width="2"/>
                                <rect x="30" y="36" width="8" height="3" rx="1" fill="#6B7280"/>
                            </svg>
                        </div>
                        <div class="icon-name">Scooter</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;scooterGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#8B5CF6&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#7C3AED&quot;/></linearGradient></defs><ellipse cx=&quot;32&quot; cy=&quot;50&quot; rx=&quot;26&quot; ry=&quot;3&quot; fill=&quot;#374151&quot; opacity=&quot;0.2&quot;/><circle cx=&quot;16&quot; cy=&quot;40&quot; r=&quot;8&quot; fill=&quot;#1F2937&quot;/><circle cx=&quot;48&quot; cy=&quot;40&quot; r=&quot;6&quot; fill=&quot;#1F2937&quot;/><circle cx=&quot;16&quot; cy=&quot;40&quot; r=&quot;6&quot; fill=&quot;#6B7280&quot;/><circle cx=&quot;48&quot; cy=&quot;40&quot; r=&quot;4&quot; fill=&quot;#6B7280&quot;/><rect x=&quot;24&quot; y=&quot;32&quot; width=&quot;16&quot; height=&quot;4&quot; rx=&quot;2&quot; fill=&quot;url(#scooterGrad)&quot;/><rect x=&quot;32&quot; y=&quot;20&quot; width=&quot;4&quot; height=&quot;16&quot; rx=&quot;2&quot; fill=&quot;url(#scooterGrad)&quot;/><rect x=&quot;28&quot; y=&quot;16&quot; width=&quot;12&quot; height=&quot;8&quot; rx=&quot;4&quot; fill=&quot;url(#scooterGrad)&quot;/><circle cx=&quot;34&quot; cy=&quot;20&quot; r=&quot;2&quot; fill=&quot;#FBBF24&quot;/><line x1=&quot;24&quot; y1=&quot;34&quot; x2=&quot;16&quot; y2=&quot;40&quot; stroke=&quot;url(#scooterGrad)&quot; stroke-width=&quot;2&quot;/><line x1=&quot;40&quot; y1=&quot;34&quot; x2=&quot;48&quot; y2=&quot;40&quot; stroke=&quot;url(#scooterGrad)&quot; stroke-width=&quot;2&quot;/><rect x=&quot;30&quot; y=&quot;36&quot; width=&quot;8&quot; height=&quot;3&quot; rx=&quot;1&quot; fill=&quot;#6B7280&quot;/></svg>')">Copy SVG</button>
                    </div>
                </div>
            </section>

            <!-- Utility Icons Category -->
            <section class="category-section">
                <div class="category-header">
                    <h2 class="category-title">Utility Icons</h2>
                    <span class="category-count">8 icons</span>
                </div>
                <div class="icon-grid">
                    <!-- Wrench Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="wrenchGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#6B7280"/>
                                        <stop offset="100%" style="stop-color:#4B5563"/>
                                    </linearGradient>
                                </defs>
                                <path d="M16 48 L48 16 Q52 12 56 16 Q52 20 48 16 L44 20 Q40 16 44 12 Q48 16 44 20 L16 48 Q12 52 8 48 Q12 44 16 48 Z" fill="url(#wrenchGrad)"/>
                                <circle cx="48" cy="16" r="4" fill="#EF4444"/>
                                <circle cx="16" cy="48" r="4" fill="#EF4444"/>
                                <rect x="28" y="28" width="8" height="8" rx="2" fill="#FBBF24"/>
                                <path d="M20 44 L44 20" stroke="#F59E0B" stroke-width="2"/>
                            </svg>
                        </div>
                        <div class="icon-name">Wrench</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;wrenchGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#6B7280&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#4B5563&quot;/></linearGradient></defs><path d=&quot;M16 48 L48 16 Q52 12 56 16 Q52 20 48 16 L44 20 Q40 16 44 12 Q48 16 44 20 L16 48 Q12 52 8 48 Q12 44 16 48 Z&quot; fill=&quot;url(#wrenchGrad)&quot;/><circle cx=&quot;48&quot; cy=&quot;16&quot; r=&quot;4&quot; fill=&quot;#EF4444&quot;/><circle cx=&quot;16&quot; cy=&quot;48&quot; r=&quot;4&quot; fill=&quot;#EF4444&quot;/><rect x=&quot;28&quot; y=&quot;28&quot; width=&quot;8&quot; height=&quot;8&quot; rx=&quot;2&quot; fill=&quot;#FBBF24&quot;/><path d=&quot;M20 44 L44 20&quot; stroke=&quot;#F59E0B&quot; stroke-width=&quot;2&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Hammer Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="hammerGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#8B4513"/>
                                        <stop offset="100%" style="stop-color:#654321"/>
                                    </linearGradient>
                                </defs>
                                <rect x="28" y="32" width="8" height="24" rx="4" fill="url(#hammerGrad)"/>
                                <rect x="16" y="16" width="32" height="12" rx="6" fill="#6B7280"/>
                                <rect x="18" y="18" width="28" height="8" rx="4" fill="#9CA3AF"/>
                                <circle cx="32" cy="22" r="3" fill="#EF4444"/>
                                <rect x="30" y="52" width="4" height="4" rx="2" fill="#4B5563"/>
                            </svg>
                        </div>
                        <div class="icon-name">Hammer</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;hammerGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#8B4513&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#654321&quot;/></linearGradient></defs><rect x=&quot;28&quot; y=&quot;32&quot; width=&quot;8&quot; height=&quot;24&quot; rx=&quot;4&quot; fill=&quot;url(#hammerGrad)&quot;/><rect x=&quot;16&quot; y=&quot;16&quot; width=&quot;32&quot; height=&quot;12&quot; rx=&quot;6&quot; fill=&quot;#6B7280&quot;/><rect x=&quot;18&quot; y=&quot;18&quot; width=&quot;28&quot; height=&quot;8&quot; rx=&quot;4&quot; fill=&quot;#9CA3AF&quot;/><circle cx=&quot;32&quot; cy=&quot;22&quot; r=&quot;3&quot; fill=&quot;#EF4444&quot;/><rect x=&quot;30&quot; y=&quot;52&quot; width=&quot;4&quot; height=&quot;4&quot; rx=&quot;2&quot; fill=&quot;#4B5563&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Settings Gear Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="gearGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#3B82F6"/>
                                        <stop offset="100%" style="stop-color:#2563EB"/>
                                    </linearGradient>
                                </defs>
                                <path d="M32 8 L36 12 L40 8 L44 12 L48 8 L52 12 L56 16 L52 20 L56 24 L52 28 L56 32 L52 36 L56 40 L52 44 L48 48 L44 52 L40 48 L36 52 L32 48 L28 52 L24 48 L20 52 L16 48 L12 44 L16 40 L12 36 L16 32 L12 28 L16 24 L12 20 L16 16 L20 12 L24 8 L28 12 Z" fill="url(#gearGrad)"/>
                                <circle cx="32" cy="32" r="8" fill="#FFFFFF"/>
                                <circle cx="32" cy="32" r="4" fill="url(#gearGrad)"/>
                            </svg>
                        </div>
                        <div class="icon-name">Settings</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;gearGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#3B82F6&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#2563EB&quot;/></linearGradient></defs><path d=&quot;M32 8 L36 12 L40 8 L44 12 L48 8 L52 12 L56 16 L52 20 L56 24 L52 28 L56 32 L52 36 L56 40 L52 44 L48 48 L44 52 L40 48 L36 52 L32 48 L28 52 L24 48 L20 52 L16 48 L12 44 L16 40 L12 36 L16 32 L12 28 L16 24 L12 20 L16 16 L20 12 L24 8 L28 12 Z&quot; fill=&quot;url(#gearGrad)&quot;/><circle cx=&quot;32&quot; cy=&quot;32&quot; r=&quot;8&quot; fill=&quot;#FFFFFF&quot;/><circle cx=&quot;32&quot; cy=&quot;32&quot; r=&quot;4&quot; fill=&quot;url(#gearGrad)&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Screwdriver Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="screwGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#FBBF24"/>
                                        <stop offset="100%" style="stop-color:#F59E0B"/>
                                    </linearGradient>
                                </defs>
                                <rect x="28" y="8" width="8" height="32" rx="4" fill="url(#screwGrad)"/>
                                <rect x="26" y="40" width="12" height="16" rx="6" fill="#EF4444"/>
                                <rect x="28" y="42" width="8" height="12" rx="4" fill="#DC2626"/>
                                <circle cx="32" cy="48" r="2" fill="#FBBF24"/>
                                <rect x="30" y="12" width="4" height="24" rx="2" fill="#6B7280"/>
                            </svg>
                        </div>
                        <div class="icon-name">Screwdriver</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;screwGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#FBBF24&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#F59E0B&quot;/></linearGradient></defs><rect x=&quot;28&quot; y=&quot;8&quot; width=&quot;8&quot; height=&quot;32&quot; rx=&quot;4&quot; fill=&quot;url(#screwGrad)&quot;/><rect x=&quot;26&quot; y=&quot;40&quot; width=&quot;12&quot; height=&quot;16&quot; rx=&quot;6&quot; fill=&quot;#EF4444&quot;/><rect x=&quot;28&quot; y=&quot;42&quot; width=&quot;8&quot; height=&quot;12&quot; rx=&quot;4&quot; fill=&quot;#DC2626&quot;/><circle cx=&quot;32&quot; cy=&quot;48&quot; r=&quot;2&quot; fill=&quot;#FBBF24&quot;/><rect x=&quot;30&quot; y=&quot;12&quot; width=&quot;4&quot; height=&quot;24&quot; rx=&quot;2&quot; fill=&quot;#6B7280&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Toolbox Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="toolboxGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#EF4444"/>
                                        <stop offset="100%" style="stop-color:#DC2626"/>
                                    </linearGradient>
                                </defs>
                                <rect x="8" y="28" width="48" height="24" rx="4" fill="url(#toolboxGrad)"/>
                                <rect x="20" y="20" width="24" height="12" rx="2" fill="url(#toolboxGrad)"/>
                                <rect x="12" y="32" width="40" height="16" rx="2" fill="#FFFFFF" opacity="0.2"/>
                                <circle cx="32" cy="40" r="3" fill="#FBBF24"/>
                                <rect x="29" y="38" width="6" height="4" rx="1" fill="#FFFFFF"/>
                                <rect x="16" y="36" width="8" height="2" rx="1" fill="#6B7280"/>
                                <rect x="40" y="36" width="8" height="2" rx="1" fill="#6B7280"/>
                                <rect x="24" y="44" width="16" height="2" rx="1" fill="#6B7280"/>
                            </svg>
                        </div>
                        <div class="icon-name">Toolbox</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;toolboxGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#EF4444&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#DC2626&quot;/></linearGradient></defs><rect x=&quot;8&quot; y=&quot;28&quot; width=&quot;48&quot; height=&quot;24&quot; rx=&quot;4&quot; fill=&quot;url(#toolboxGrad)&quot;/><rect x=&quot;20&quot; y=&quot;20&quot; width=&quot;24&quot; height=&quot;12&quot; rx=&quot;2&quot; fill=&quot;url(#toolboxGrad)&quot;/><rect x=&quot;12&quot; y=&quot;32&quot; width=&quot;40&quot; height=&quot;16&quot; rx=&quot;2&quot; fill=&quot;#FFFFFF&quot; opacity=&quot;0.2&quot;/><circle cx=&quot;32&quot; cy=&quot;40&quot; r=&quot;3&quot; fill=&quot;#FBBF24&quot;/><rect x=&quot;29&quot; y=&quot;38&quot; width=&quot;6&quot; height=&quot;4&quot; rx=&quot;1&quot; fill=&quot;#FFFFFF&quot;/><rect x=&quot;16&quot; y=&quot;36&quot; width=&quot;8&quot; height=&quot;2&quot; rx=&quot;1&quot; fill=&quot;#6B7280&quot;/><rect x=&quot;40&quot; y=&quot;36&quot; width=&quot;8&quot; height=&quot;2&quot; rx=&quot;1&quot; fill=&quot;#6B7280&quot;/><rect x=&quot;24&quot; y=&quot;44&quot; width=&quot;16&quot; height=&quot;2&quot; rx=&quot;1&quot; fill=&quot;#6B7280&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Drill Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="drillGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#10B981"/>
                                        <stop offset="100%" style="stop-color:#059669"/>
                                    </linearGradient>
                                </defs>
                                <rect x="12" y="24" width="32" height="16" rx="8" fill="url(#drillGrad)"/>
                                <rect x="44" y="28" width="12" height="8" rx="4" fill="#6B7280"/>
                                <circle cx="50" cy="32" r="2" fill="#FBBF24"/>
                                <rect x="16" y="28" width="24" height="8" rx="4" fill="#FFFFFF" opacity="0.3"/>
                                <rect x="8" y="30" width="8" height="4" rx="2" fill="#6B7280"/>
                                <circle cx="4" cy="32" r="2" fill="#EF4444"/>
                                <rect x="20" y="40" width="8" height="8" rx="4" fill="url(#drillGrad)"/>
                                <rect x="22" y="42" width="4" height="4" rx="2" fill="#FFFFFF" opacity="0.8"/>
                            </svg>
                        </div>
                        <div class="icon-name">Drill</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;drillGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#10B981&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#059669&quot;/></linearGradient></defs><rect x=&quot;12&quot; y=&quot;24&quot; width=&quot;32&quot; height=&quot;16&quot; rx=&quot;8&quot; fill=&quot;url(#drillGrad)&quot;/><rect x=&quot;44&quot; y=&quot;28&quot; width=&quot;12&quot; height=&quot;8&quot; rx=&quot;4&quot; fill=&quot;#6B7280&quot;/><circle cx=&quot;50&quot; cy=&quot;32&quot; r=&quot;2&quot; fill=&quot;#FBBF24&quot;/><rect x=&quot;16&quot; y=&quot;28&quot; width=&quot;24&quot; height=&quot;8&quot; rx=&quot;4&quot; fill=&quot;#FFFFFF&quot; opacity=&quot;0.3&quot;/><rect x=&quot;8&quot; y=&quot;30&quot; width=&quot;8&quot; height=&quot;4&quot; rx=&quot;2&quot; fill=&quot;#6B7280&quot;/><circle cx=&quot;4&quot; cy=&quot;32&quot; r=&quot;2&quot; fill=&quot;#EF4444&quot;/><rect x=&quot;20&quot; y=&quot;40&quot; width=&quot;8&quot; height=&quot;8&quot; rx=&quot;4&quot; fill=&quot;url(#drillGrad)&quot;/><rect x=&quot;22&quot; y=&quot;42&quot; width=&quot;4&quot; height=&quot;4&quot; rx=&quot;2&quot; fill=&quot;#FFFFFF&quot; opacity=&quot;0.8&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Pliers Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="pliersGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#8B5CF6"/>
                                        <stop offset="100%" style="stop-color:#7C3AED"/>
                                    </linearGradient>
                                </defs>
                                <path d="M16 8 Q20 12 24 16 L32 24 L40 16 Q44 12 48 8 Q52 12 48 16 L40 24 L32 32 L24 24 L16 16 Q12 12 16 8 Z" fill="url(#pliersGrad)"/>
                                <path d="M24 24 L16 32 Q12 36 8 40 Q4 44 8 48 Q12 44 16 40 L24 32" fill="url(#pliersGrad)"/>
                                <path d="M40 24 L48 32 Q52 36 56 40 Q60 44 56 48 Q52 44 48 40 L40 32" fill="url(#pliersGrad)"/>
                                <circle cx="32" cy="24" r="3" fill="#FBBF24"/>
                                <rect x="30" y="30" width="4" height="8" rx="2" fill="#EF4444"/>
                            </svg>
                        </div>
                        <div class="icon-name">Pliers</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;pliersGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#8B5CF6&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#7C3AED&quot;/></linearGradient></defs><path d=&quot;M16 8 Q20 12 24 16 L32 24 L40 16 Q44 12 48 8 Q52 12 48 16 L40 24 L32 32 L24 24 L16 16 Q12 12 16 8 Z&quot; fill=&quot;url(#pliersGrad)&quot;/><path d=&quot;M24 24 L16 32 Q12 36 8 40 Q4 44 8 48 Q12 44 16 40 L24 32&quot; fill=&quot;url(#pliersGrad)&quot;/><path d=&quot;M40 24 L48 32 Q52 36 56 40 Q60 44 56 48 Q52 44 48 40 L40 32&quot; fill=&quot;url(#pliersGrad)&quot;/><circle cx=&quot;32&quot; cy=&quot;24&quot; r=&quot;3&quot; fill=&quot;#FBBF24&quot;/><rect x=&quot;30&quot; y=&quot;30&quot; width=&quot;4&quot; height=&quot;8&quot; rx=&quot;2&quot; fill=&quot;#EF4444&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Saw Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="sawGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#F59E0B"/>
                                        <stop offset="100%" style="stop-color:#D97706"/>
                                    </linearGradient>
                                </defs>
                                <rect x="8" y="28" width="48" height="8" rx="4" fill="url(#sawGrad)"/>
                                <path d="M8 36 L12 40 L16 36 L20 40 L24 36 L28 40 L32 36 L36 40 L40 36 L44 40 L48 36 L52 40 L56 36" stroke="#6B7280" stroke-width="2" fill="none"/>
                                <rect x="48" y="20" width="8" height="16" rx="4" fill="#8B4513"/>
                                <rect x="50" y="22" width="4" height="12" rx="2" fill="#A0522D"/>
                                <circle cx="52" cy="28" r="2" fill="#FBBF24"/>
                            </svg>
                        </div>
                        <div class="icon-name">Saw</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;sawGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#F59E0B&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#D97706&quot;/></linearGradient></defs><rect x=&quot;8&quot; y=&quot;28&quot; width=&quot;48&quot; height=&quot;8&quot; rx=&quot;4&quot; fill=&quot;url(#sawGrad)&quot;/><path d=&quot;M8 36 L12 40 L16 36 L20 40 L24 36 L28 40 L32 36 L36 40 L40 36 L44 40 L48 36 L52 40 L56 36&quot; stroke=&quot;#6B7280&quot; stroke-width=&quot;2&quot; fill=&quot;none&quot;/><rect x=&quot;48&quot; y=&quot;20&quot; width=&quot;8&quot; height=&quot;16&quot; rx=&quot;4&quot; fill=&quot;#8B4513&quot;/><rect x=&quot;50&quot; y=&quot;22&quot; width=&quot;4&quot; height=&quot;12&quot; rx=&quot;2&quot; fill=&quot;#A0522D&quot;/><circle cx=&quot;52&quot; cy=&quot;28&quot; r=&quot;2&quot; fill=&quot;#FBBF24&quot;/></svg>')">Copy SVG</button>
                    </div>
                </div>
            </section>

            <!-- Communication Icons Category -->
            <section class="category-section">
                <div class="category-header">
                    <h2 class="category-title">Communication Icons</h2>
                    <span class="category-count">8 icons</span>
                </div>
                <div class="icon-grid">
                    <!-- Phone Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="phoneGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#10B981"/>
                                        <stop offset="100%" style="stop-color:#059669"/>
                                    </linearGradient>
                                </defs>
                                <rect x="20" y="8" width="24" height="48" rx="8" fill="url(#phoneGrad)"/>
                                <rect x="24" y="16" width="16" height="32" rx="2" fill="#FFFFFF"/>
                                <circle cx="32" cy="52" r="3" fill="#FFFFFF"/>
                                <rect x="28" y="12" width="8" height="2" rx="1" fill="#FFFFFF"/>
                                <circle cx="26" cy="20" r="1" fill="#EF4444"/>
                                <circle cx="30" cy="20" r="1" fill="#FBBF24"/>
                                <circle cx="34" cy="20" r="1" fill="#10B981"/>
                                <circle cx="38" cy="20" r="1" fill="#3B82F6"/>
                            </svg>
                        </div>
                        <div class="icon-name">Phone</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;phoneGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#10B981&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#059669&quot;/></linearGradient></defs><rect x=&quot;20&quot; y=&quot;8&quot; width=&quot;24&quot; height=&quot;48&quot; rx=&quot;8&quot; fill=&quot;url(#phoneGrad)&quot;/><rect x=&quot;24&quot; y=&quot;16&quot; width=&quot;16&quot; height=&quot;32&quot; rx=&quot;2&quot; fill=&quot;#FFFFFF&quot;/><circle cx=&quot;32&quot; cy=&quot;52&quot; r=&quot;3&quot; fill=&quot;#FFFFFF&quot;/><rect x=&quot;28&quot; y=&quot;12&quot; width=&quot;8&quot; height=&quot;2&quot; rx=&quot;1&quot; fill=&quot;#FFFFFF&quot;/><circle cx=&quot;26&quot; cy=&quot;20&quot; r=&quot;1&quot; fill=&quot;#EF4444&quot;/><circle cx=&quot;30&quot; cy=&quot;20&quot; r=&quot;1&quot; fill=&quot;#FBBF24&quot;/><circle cx=&quot;34&quot; cy=&quot;20&quot; r=&quot;1&quot; fill=&quot;#10B981&quot;/><circle cx=&quot;38&quot; cy=&quot;20&quot; r=&quot;1&quot; fill=&quot;#3B82F6&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Email Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="emailGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#3B82F6"/>
                                        <stop offset="100%" style="stop-color:#2563EB"/>
                                    </linearGradient>
                                </defs>
                                <rect x="8" y="16" width="48" height="32" rx="4" fill="url(#emailGrad)"/>
                                <path d="M8 16 L32 32 L56 16" stroke="#FFFFFF" stroke-width="2" fill="none"/>
                                <rect x="12" y="20" width="40" height="24" rx="2" fill="#FFFFFF" opacity="0.9"/>
                                <path d="M12 20 L32 36 L52 20" stroke="url(#emailGrad)" stroke-width="2" fill="none"/>
                                <circle cx="48" cy="24" r="3" fill="#EF4444"/>
                                <text x="48" y="27" text-anchor="middle" fill="white" font-size="6" font-weight="bold">!</text>
                            </svg>
                        </div>
                        <div class="icon-name">Email</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;emailGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#3B82F6&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#2563EB&quot;/></linearGradient></defs><rect x=&quot;8&quot; y=&quot;16&quot; width=&quot;48&quot; height=&quot;32&quot; rx=&quot;4&quot; fill=&quot;url(#emailGrad)&quot;/><path d=&quot;M8 16 L32 32 L56 16&quot; stroke=&quot;#FFFFFF&quot; stroke-width=&quot;2&quot; fill=&quot;none&quot;/><rect x=&quot;12&quot; y=&quot;20&quot; width=&quot;40&quot; height=&quot;24&quot; rx=&quot;2&quot; fill=&quot;#FFFFFF&quot; opacity=&quot;0.9&quot;/><path d=&quot;M12 20 L32 36 L52 20&quot; stroke=&quot;url(#emailGrad)&quot; stroke-width=&quot;2&quot; fill=&quot;none&quot;/><circle cx=&quot;48&quot; cy=&quot;24&quot; r=&quot;3&quot; fill=&quot;#EF4444&quot;/><text x=&quot;48&quot; y=&quot;27&quot; text-anchor=&quot;middle&quot; fill=&quot;white&quot; font-size=&quot;6&quot; font-weight=&quot;bold&quot;>!</text></svg>')">Copy SVG</button>
                    </div>

                    <!-- Chat Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="chatGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#FBBF24"/>
                                        <stop offset="100%" style="stop-color:#F59E0B"/>
                                    </linearGradient>
                                </defs>
                                <rect x="8" y="12" width="36" height="24" rx="12" fill="url(#chatGrad)"/>
                                <path d="M20 36 L26 42 L32 36" fill="url(#chatGrad)"/>
                                <rect x="12" y="16" width="28" height="16" rx="8" fill="#FFFFFF" opacity="0.9"/>
                                <circle cx="18" cy="24" r="2" fill="url(#chatGrad)"/>
                                <circle cx="26" cy="24" r="2" fill="url(#chatGrad)"/>
                                <circle cx="34" cy="24" r="2" fill="url(#chatGrad)"/>
                                <rect x="32" y="20" width="24" height="20" rx="10" fill="#8B5CF6" opacity="0.8"/>
                                <circle cx="40" cy="30" r="1" fill="#FFFFFF"/>
                                <circle cx="44" cy="30" r="1" fill="#FFFFFF"/>
                                <circle cx="48" cy="30" r="1" fill="#FFFFFF"/>
                            </svg>
                        </div>
                        <div class="icon-name">Chat</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;chatGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#FBBF24&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#F59E0B&quot;/></linearGradient></defs><rect x=&quot;8&quot; y=&quot;12&quot; width=&quot;36&quot; height=&quot;24&quot; rx=&quot;12&quot; fill=&quot;url(#chatGrad)&quot;/><path d=&quot;M20 36 L26 42 L32 36&quot; fill=&quot;url(#chatGrad)&quot;/><rect x=&quot;12&quot; y=&quot;16&quot; width=&quot;28&quot; height=&quot;16&quot; rx=&quot;8&quot; fill=&quot;#FFFFFF&quot; opacity=&quot;0.9&quot;/><circle cx=&quot;18&quot; cy=&quot;24&quot; r=&quot;2&quot; fill=&quot;url(#chatGrad)&quot;/><circle cx=&quot;26&quot; cy=&quot;24&quot; r=&quot;2&quot; fill=&quot;url(#chatGrad)&quot;/><circle cx=&quot;34&quot; cy=&quot;24&quot; r=&quot;2&quot; fill=&quot;url(#chatGrad)&quot;/><rect x=&quot;32&quot; y=&quot;20&quot; width=&quot;24&quot; height=&quot;20&quot; rx=&quot;10&quot; fill=&quot;#8B5CF6&quot; opacity=&quot;0.8&quot;/><circle cx=&quot;40&quot; cy=&quot;30&quot; r=&quot;1&quot; fill=&quot;#FFFFFF&quot;/><circle cx=&quot;44&quot; cy=&quot;30&quot; r=&quot;1&quot; fill=&quot;#FFFFFF&quot;/><circle cx=&quot;48&quot; cy=&quot;30&quot; r=&quot;1&quot; fill=&quot;#FFFFFF&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Video Call Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="videoGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#EF4444"/>
                                        <stop offset="100%" style="stop-color:#DC2626"/>
                                    </linearGradient>
                                </defs>
                                <rect x="8" y="20" width="32" height="24" rx="4" fill="url(#videoGrad)"/>
                                <rect x="12" y="24" width="24" height="16" rx="2" fill="#FFFFFF" opacity="0.9"/>
                                <path d="M40 24 L52 16 L52 48 L40 40 Z" fill="url(#videoGrad)"/>
                                <circle cx="20" cy="30" r="3" fill="#3B82F6"/>
                                <circle cx="28" cy="30" r="3" fill="#10B981"/>
                                <rect x="16" y="34" width="12" height="4" rx="2" fill="#6B7280"/>
                                <circle cx="48" cy="12" r="4" fill="#FBBF24"/>
                                <rect x="46" y="10" width="4" height="4" rx="2" fill="#FFFFFF"/>
                            </svg>
                        </div>
                        <div class="icon-name">Video Call</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;videoGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#EF4444&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#DC2626&quot;/></linearGradient></defs><rect x=&quot;8&quot; y=&quot;20&quot; width=&quot;32&quot; height=&quot;24&quot; rx=&quot;4&quot; fill=&quot;url(#videoGrad)&quot;/><rect x=&quot;12&quot; y=&quot;24&quot; width=&quot;24&quot; height=&quot;16&quot; rx=&quot;2&quot; fill=&quot;#FFFFFF&quot; opacity=&quot;0.9&quot;/><path d=&quot;M40 24 L52 16 L52 48 L40 40 Z&quot; fill=&quot;url(#videoGrad)&quot;/><circle cx=&quot;20&quot; cy=&quot;30&quot; r=&quot;3&quot; fill=&quot;#3B82F6&quot;/><circle cx=&quot;28&quot; cy=&quot;30&quot; r=&quot;3&quot; fill=&quot;#10B981&quot;/><rect x=&quot;16&quot; y=&quot;34&quot; width=&quot;12&quot; height=&quot;4&quot; rx=&quot;2&quot; fill=&quot;#6B7280&quot;/><circle cx=&quot;48&quot; cy=&quot;12&quot; r=&quot;4&quot; fill=&quot;#FBBF24&quot;/><rect x=&quot;46&quot; y=&quot;10&quot; width=&quot;4&quot; height=&quot;4&quot; rx=&quot;2&quot; fill=&quot;#FFFFFF&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Message Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="messageGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#8B5CF6"/>
                                        <stop offset="100%" style="stop-color:#7C3AED"/>
                                    </linearGradient>
                                </defs>
                                <rect x="8" y="16" width="48" height="32" rx="8" fill="url(#messageGrad)"/>
                                <rect x="12" y="20" width="40" height="24" rx="4" fill="#FFFFFF" opacity="0.9"/>
                                <rect x="16" y="24" width="24" height="2" rx="1" fill="url(#messageGrad)"/>
                                <rect x="16" y="28" width="32" height="2" rx="1" fill="url(#messageGrad)"/>
                                <rect x="16" y="32" width="20" height="2" rx="1" fill="url(#messageGrad)"/>
                                <rect x="16" y="36" width="28" height="2" rx="1" fill="url(#messageGrad)"/>
                                <circle cx="48" cy="12" r="6" fill="#EF4444"/>
                                <text x="48" y="16" text-anchor="middle" fill="white" font-size="8" font-weight="bold">3</text>
                            </svg>
                        </div>
                        <div class="icon-name">Message</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;messageGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#8B5CF6&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#7C3AED&quot;/></linearGradient></defs><rect x=&quot;8&quot; y=&quot;16&quot; width=&quot;48&quot; height=&quot;32&quot; rx=&quot;8&quot; fill=&quot;url(#messageGrad)&quot;/><rect x=&quot;12&quot; y=&quot;20&quot; width=&quot;40&quot; height=&quot;24&quot; rx=&quot;4&quot; fill=&quot;#FFFFFF&quot; opacity=&quot;0.9&quot;/><rect x=&quot;16&quot; y=&quot;24&quot; width=&quot;24&quot; height=&quot;2&quot; rx=&quot;1&quot; fill=&quot;url(#messageGrad)&quot;/><rect x=&quot;16&quot; y=&quot;28&quot; width=&quot;32&quot; height=&quot;2&quot; rx=&quot;1&quot; fill=&quot;url(#messageGrad)&quot;/><rect x=&quot;16&quot; y=&quot;32&quot; width=&quot;20&quot; height=&quot;2&quot; rx=&quot;1&quot; fill=&quot;url(#messageGrad)&quot;/><rect x=&quot;16&quot; y=&quot;36&quot; width=&quot;28&quot; height=&quot;2&quot; rx=&quot;1&quot; fill=&quot;url(#messageGrad)&quot;/><circle cx=&quot;48&quot; cy=&quot;12&quot; r=&quot;6&quot; fill=&quot;#EF4444&quot;/><text x=&quot;48&quot; y=&quot;16&quot; text-anchor=&quot;middle&quot; fill=&quot;white&quot; font-size=&quot;8&quot; font-weight=&quot;bold&quot;>3</text></svg>')">Copy SVG</button>
                    </div>

                    <!-- Notification Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="notifGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#F59E0B"/>
                                        <stop offset="100%" style="stop-color:#D97706"/>
                                    </linearGradient>
                                </defs>
                                <path d="M32 8 Q40 12 40 24 Q40 32 44 36 L20 36 Q24 32 24 24 Q24 12 32 8 Z" fill="url(#notifGrad)"/>
                                <rect x="28" y="36" width="8" height="4" rx="2" fill="url(#notifGrad)"/>
                                <path d="M28 40 Q32 44 36 40" stroke="url(#notifGrad)" stroke-width="2" fill="none"/>
                                <circle cx="48" cy="16" r="8" fill="#EF4444"/>
                                <text x="48" y="20" text-anchor="middle" fill="white" font-size="10" font-weight="bold">!</text>
                                <circle cx="32" cy="20" r="2" fill="#FFFFFF" opacity="0.8"/>
                                <rect x="30" y="24" width="4" height="8" rx="2" fill="#FFFFFF" opacity="0.6"/>
                            </svg>
                        </div>
                        <div class="icon-name">Notification</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;notifGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#F59E0B&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#D97706&quot;/></linearGradient></defs><path d=&quot;M32 8 Q40 12 40 24 Q40 32 44 36 L20 36 Q24 32 24 24 Q24 12 32 8 Z&quot; fill=&quot;url(#notifGrad)&quot;/><rect x=&quot;28&quot; y=&quot;36&quot; width=&quot;8&quot; height=&quot;4&quot; rx=&quot;2&quot; fill=&quot;url(#notifGrad)&quot;/><path d=&quot;M28 40 Q32 44 36 40&quot; stroke=&quot;url(#notifGrad)&quot; stroke-width=&quot;2&quot; fill=&quot;none&quot;/><circle cx=&quot;48&quot; cy=&quot;16&quot; r=&quot;8&quot; fill=&quot;#EF4444&quot;/><text x=&quot;48&quot; y=&quot;20&quot; text-anchor=&quot;middle&quot; fill=&quot;white&quot; font-size=&quot;10&quot; font-weight=&quot;bold&quot;>!</text><circle cx=&quot;32&quot; cy=&quot;20&quot; r=&quot;2&quot; fill=&quot;#FFFFFF&quot; opacity=&quot;0.8&quot;/><rect x=&quot;30&quot; y=&quot;24&quot; width=&quot;4&quot; height=&quot;8&quot; rx=&quot;2&quot; fill=&quot;#FFFFFF&quot; opacity=&quot;0.6&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Microphone Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="micGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#6B7280"/>
                                        <stop offset="100%" style="stop-color:#4B5563"/>
                                    </linearGradient>
                                </defs>
                                <rect x="28" y="8" width="8" height="24" rx="4" fill="url(#micGrad)"/>
                                <rect x="30" y="12" width="4" height="16" rx="2" fill="#9CA3AF"/>
                                <path d="M20 28 Q20 36 32 36 Q44 36 44 28" stroke="url(#micGrad)" stroke-width="3" fill="none"/>
                                <line x1="32" y1="36" x2="32" y2="48" stroke="url(#micGrad)" stroke-width="3"/>
                                <rect x="24" y="48" width="16" height="4" rx="2" fill="url(#micGrad)"/>
                                <circle cx="32" cy="16" r="2" fill="#EF4444"/>
                                <rect x="30" y="20" width="4" height="2" rx="1" fill="#10B981"/>
                                <rect x="30" y="24" width="4" height="2" rx="1" fill="#10B981"/>
                            </svg>
                        </div>
                        <div class="icon-name">Microphone</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;micGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#6B7280&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#4B5563&quot;/></linearGradient></defs><rect x=&quot;28&quot; y=&quot;8&quot; width=&quot;8&quot; height=&quot;24&quot; rx=&quot;4&quot; fill=&quot;url(#micGrad)&quot;/><rect x=&quot;30&quot; y=&quot;12&quot; width=&quot;4&quot; height=&quot;16&quot; rx=&quot;2&quot; fill=&quot;#9CA3AF&quot;/><path d=&quot;M20 28 Q20 36 32 36 Q44 36 44 28&quot; stroke=&quot;url(#micGrad)&quot; stroke-width=&quot;3&quot; fill=&quot;none&quot;/><line x1=&quot;32&quot; y1=&quot;36&quot; x2=&quot;32&quot; y2=&quot;48&quot; stroke=&quot;url(#micGrad)&quot; stroke-width=&quot;3&quot;/><rect x=&quot;24&quot; y=&quot;48&quot; width=&quot;16&quot; height=&quot;4&quot; rx=&quot;2&quot; fill=&quot;url(#micGrad)&quot;/><circle cx=&quot;32&quot; cy=&quot;16&quot; r=&quot;2&quot; fill=&quot;#EF4444&quot;/><rect x=&quot;30&quot; y=&quot;20&quot; width=&quot;4&quot; height=&quot;2&quot; rx=&quot;1&quot; fill=&quot;#10B981&quot;/><rect x=&quot;30&quot; y=&quot;24&quot; width=&quot;4&quot; height=&quot;2&quot; rx=&quot;1&quot; fill=&quot;#10B981&quot;/></svg>')">Copy SVG</button>
                    </div>

                    <!-- Speaker Icon -->
                    <div class="icon-card">
                        <div class="icon-container">
                            <svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="speakerGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#10B981"/>
                                        <stop offset="100%" style="stop-color:#059669"/>
                                    </linearGradient>
                                </defs>
                                <rect x="8" y="24" width="12" height="16" rx="2" fill="url(#speakerGrad)"/>
                                <path d="M20 24 L32 16 L32 48 L20 40" fill="url(#speakerGrad)"/>
                                <path d="M36 20 Q44 24 44 32 Q44 40 36 44" stroke="url(#speakerGrad)" stroke-width="3" fill="none" stroke-linecap="round"/>
                                <path d="M40 24 Q46 28 46 32 Q46 36 40 40" stroke="url(#speakerGrad)" stroke-width="2" fill="none" stroke-linecap="round"/>
                                <path d="M44 28 Q48 30 48 32 Q48 34 44 36" stroke="url(#speakerGrad)" stroke-width="2" fill="none" stroke-linecap="round"/>
                                <circle cx="14" cy="32" r="2" fill="#FFFFFF" opacity="0.8"/>
                            </svg>
                        </div>
                        <div class="icon-name">Speaker</div>
                        <button class="copy-button" onclick="copySVGCode(this, '<svg viewBox=&quot;0 0 64 64&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;><defs><linearGradient id=&quot;speakerGrad&quot; x1=&quot;0%&quot; y1=&quot;0%&quot; x2=&quot;100%&quot; y2=&quot;100%&quot;><stop offset=&quot;0%&quot; style=&quot;stop-color:#10B981&quot;/><stop offset=&quot;100%&quot; style=&quot;stop-color:#059669&quot;/></linearGradient></defs><rect x=&quot;8&quot; y=&quot;24&quot; width=&quot;12&quot; height=&quot;16&quot; rx=&quot;2&quot; fill=&quot;url(#speakerGrad)&quot;/><path d=&quot;M20 24 L32 16 L32 48 L20 40&quot; fill=&quot;url(#speakerGrad)&quot;/><path d=&quot;M36 20 Q44 24 44 32 Q44 40 36 44&quot; stroke=&quot;url(#speakerGrad)&quot; stroke-width=&quot;3&quot; fill=&quot;none&quot; stroke-linecap=&quot;round&quot;/><path d=&quot;M40 24 Q46 28 46 32 Q46 36 40 40&quot; stroke=&quot;url(#speakerGrad)&quot; stroke-width=&quot;2&quot; fill=&quot;none&quot; stroke-linecap=&quot;round&quot;/><path d=&quot;M44 28 Q48 30 48 32 Q48 34 44 36&quot; stroke=&quot;url(#speakerGrad)&quot; stroke-width=&quot;2&quot; fill=&quot;none&quot; stroke-linecap=&quot;round&quot;/><circle cx=&quot;14&quot; cy=&quot;32&quot; r=&quot;2&quot; fill=&quot;#FFFFFF&quot; opacity=&quot;0.8&quot;/></svg>')">Copy SVG</button>
                    </div>
                </div>
            </section>

            <!-- Additional Categories Notice -->
            <section class="category-section">
                <div class="category-header">
                    <h2 class="category-title">Additional Categories</h2>
                    <span class="category-count">14 more categories</span>
                </div>
                <div style="background: var(--surface-color); border: 1px solid var(--border-color); border-radius: var(--border-radius); padding: 2rem; text-align: center;">
                    <h3 style="color: var(--text-primary); margin-bottom: 1rem; font-size: 1.25rem;">Complete Icon Library</h3>
                    <p style="color: var(--text-secondary); margin-bottom: 1.5rem; line-height: 1.6;">
                        This demonstration showcases 4 complete categories with 34 professional SVG icons.
                        The full library would include all 18 requested categories with 8-12 icons each (144-216 total icons).
                    </p>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1.5rem;">
                        <div style="background: var(--background-color); padding: 1rem; border-radius: 6px; border: 1px solid var(--border-color);">
                            <h4 style="color: var(--primary-color); margin-bottom: 0.5rem;">Remaining Categories:</h4>
                            <ul style="text-align: left; color: var(--text-secondary); font-size: 0.875rem; line-height: 1.5;">
                                <li>Software Icons</li>
                                <li>Navigation Icons</li>
                                <li>Hardware Icons</li>
                                <li>Vehicle Indicators</li>
                                <li>Multimedia Icons</li>
                                <li>Analytics Icons</li>
                                <li>SEO Icons</li>
                            </ul>
                        </div>
                        <div style="background: var(--background-color); padding: 1rem; border-radius: 6px; border: 1px solid var(--border-color);">
                            <h4 style="color: var(--primary-color); margin-bottom: 0.5rem;">More Categories:</h4>
                            <ul style="text-align: left; color: var(--text-secondary); font-size: 0.875rem; line-height: 1.5;">
                                <li>AI Icons</li>
                                <li>Database Icons</li>
                                <li>Web Icons</li>
                                <li>Gamepad Icons</li>
                                <li>Light Icons</li>
                                <li>Direction Icons</li>
                                <li>Physics Icons</li>
                            </ul>
                        </div>
                    </div>
                    <div style="background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); color: white; padding: 1rem; border-radius: 6px;">
                        <p style="margin: 0; font-weight: 500;">
                            ✨ Each category follows the same professional design standards with multicolor gradients,
                            detailed styling, and consistent 64x64 viewBox dimensions for optimal scalability.
                        </p>
                    </div>
                </div>
            </section>
        </div>
    </main>
        </div>
    </main>

    <!-- Cookie Consent Banner -->
    <div class="cookie-banner" id="cookieBanner">
        <div class="cookie-content">
            <div class="cookie-text">
                <p>We use cookies to enhance your browsing experience and analyze site traffic. By continuing to use this site, you consent to our use of cookies. <a href="https://jermesa.com/privacy-policy/" target="_blank" style="color: #60a5fa;">Learn more</a></p>
            </div>
            <div class="cookie-buttons">
                <button class="cookie-button cookie-accept" onclick="acceptCookies()">Accept</button>
                <button class="cookie-button cookie-decline" onclick="declineCookies()">Decline</button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-links">
                    <a href="https://jermesa.com" target="_blank">Jermesa Studio</a>
                    <a href="https://jermesa.com/privacy-policy/" target="_blank">Privacy Policy</a>
                </div>
                <div class="attribution">
                    Created by <strong>Jermesa Studio</strong> - <a href="https://www.jermesa.com" target="_blank">www.jermesa.com</a>
                </div>
                <div class="font-attribution">
                    Fonts: Inter and JetBrains Mono by Google Fonts (Open SIL License)
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Cookie Consent Functionality
        function showCookieBanner() {
            const banner = document.getElementById('cookieBanner');
            const consent = localStorage.getItem('cookieConsent');
            
            if (!consent) {
                setTimeout(() => {
                    banner.classList.add('show');
                }, 1000);
            }
        }
        
        function acceptCookies() {
            localStorage.setItem('cookieConsent', 'accepted');
            hideCookieBanner();
        }
        
        function declineCookies() {
            localStorage.setItem('cookieConsent', 'declined');
            hideCookieBanner();
        }
        
        function hideCookieBanner() {
            const banner = document.getElementById('cookieBanner');
            banner.classList.remove('show');
        }
        
        // Copy SVG Code Functionality
        function copySVGCode(button, svgCode) {
            navigator.clipboard.writeText(svgCode).then(() => {
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                button.classList.add('copied');
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.classList.remove('copied');
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy: ', err);
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = svgCode;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                button.classList.add('copied');
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.classList.remove('copied');
                }, 2000);
            });
        }
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            showCookieBanner();
            
            // Add fade-in animation to sections
            const sections = document.querySelectorAll('.category-section');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('fade-in');
                    }
                });
            }, { threshold: 0.1 });
            
            sections.forEach(section => {
                observer.observe(section);
            });
        });
    </script>
</body>
</html>
